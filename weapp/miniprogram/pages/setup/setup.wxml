<!--setup.wxml-->
<view class="setup-container">
  <view class="game-title">
    <text>德州扑克 - 游戏设置</text>
  </view>
  <view class="setup-controls">
    <!-- 基础设置区 -->
    <view class="settings-section">
      <view class="section-header">
        <text class="section-title">⚙️ 基础设置</text>
        <text class="section-desc">游戏基本参数配置</text>
      </view>
      <view class="section-content">
        <view class="setup-row">
          <text>玩家数量 (3-10):</text>
          <input type="number" min="3" max="10" value="{{playerCount}}" bindinput="onPlayerCountInput" />
        </view>
        <view class="setup-row">
          <text>初始筹码:</text>
          <input type="number" min="100" value="{{startingChips}}" bindinput="onStartingChipsInput" />
        </view>
        <view class="setup-row">
          <text>小盲注:</text>
          <input type="number" min="1" value="{{smallBlind}}" bindinput="onSmallBlindInput" />
          <text>大盲注:</text>
          <input type="number" min="2" value="{{bigBlind}}" bindinput="onBigBlindInput" />
        </view>
        <view class="setup-row">
          <text>玩家操作等待时长(秒):</text>
          <input type="number" min="1" value="{{actionWaitSeconds}}" bindinput="onActionWaitInput" />
        </view>
        <view class="setup-row">
          <text>每局结束等待时长(秒):</text>
          <input type="number" min="1" value="{{endHandWaitSeconds}}" bindinput="onEndHandWaitInput" />
        </view>
      </view>
    </view>

    <!-- 高级设置区 -->
    <view class="settings-section">
      <view class="section-header">
        <text class="section-title">🚀 高级设置</text>
        <text class="section-desc">智能功能与特殊模式</text>
      </view>
      <view class="section-content">
        <!-- AI教学卡片 -->
        <view class="feature-card teaching-mode-section {{teachingModeUnlocked ? 'unlocked' : 'locked'}}">
      <view class="teaching-header">
        <view class="teaching-icon-wrapper">
          <text class="teaching-icon">🎓</text>
          <view class="teaching-glow" wx:if="{{teachingModeUnlocked}}"></view>
        </view>
        <view class="teaching-info">
          <text class="teaching-title">智能教学</text>
          <text class="teaching-desc">{{teachingModeUnlocked ? '实时策略指导 · 智能决策建议' : '分享解锁高级功能'}}</text>
        </view>
        <view class="teaching-control">
          <switch
            wx:if="{{teachingModeUnlocked}}"
            checked="{{teachingMode}}"
            bindchange="onTeachingModeChange"
            color="#4CAF50"
          />
          <button
            wx:else
            class="unlock-btn"
            bindtap="showUnlockTeachingMode"
            size="mini"
          >
            <text class="unlock-icon">🔓</text>
            <text class="unlock-text">解锁</text>
          </button>
        </view>
      </view>

      <view class="teaching-features" wx:if="{{teachingModeUnlocked}}">
        <view class="feature-tag">✨ 实时指导</view>
        <view class="feature-tag">🧠 智能分析</view>
        <view class="feature-tag">📚 个性教学</view>
      </view>

      <!-- 功能价值描述 -->
      <view class="teaching-value" wx:if="{{!teachingModeUnlocked}}">
        <view class="value-stats">
          <view class="stat-item">
            <text class="stat-number">+35%</text>
            <text class="stat-label">胜率提升</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">-60%</text>
            <text class="stat-label">错误决策</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">10x</text>
            <text class="stat-label">学习速度</text>
          </view>
        </view>
        <view class="value-desc">
          <text class="desc-text">🧠 AI实时分析每一手牌，提供最优策略建议</text>
          <text class="desc-text">📈 根据对手行为模式，智能调整打法风格</text>
          <text class="desc-text">🎯 精准计算胜率和底池赔率，避免情绪化决策</text>
        </view>
      </view>

      <view class="unlock-hint" wx:if="{{!teachingModeUnlocked}}">
        <text class="hint-text">💡 分享游戏给好友即可免费解锁 · 当天内持续生效</text>
      </view>


        </view>

        <!-- 鱿鱼模式卡片 -->
        <view class="feature-card squid-mode-card">
          <view class="card-header">
            <view class="card-icon-wrapper">
              <text class="card-icon">🦑</text>
            </view>
            <view class="card-info">
              <text class="card-title">鱿鱼模式</text>
              <text class="card-desc">末位淘汰制，增加游戏刺激性</text>
            </view>
            <view class="card-control">
              <switch checked="{{squidMode}}" bindchange="onSquidModeChange" color="#ff6b6b" />
            </view>
          </view>

          <view wx:if="{{squidMode}}" class="card-settings">
            <view class="setting-row">
              <text class="setting-label">惩罚金额 (每人大盲注倍数):</text>
              <input class="setting-input" type="number" min="2" max="20" value="{{squidPenaltyMultiplier}}" bindinput="onSquidPenaltyInput" />
            </view>
            <view class="penalty-display">
              <text class="penalty-text">当前惩罚: {{squidPenaltyMultiplier * bigBlind}} 筹码/人</text>
            </view>
          </view>
        </view>

        <!-- 战报分析卡片 -->
        <view class="feature-card battle-report-card">
          <view class="card-header">
            <view class="card-icon-wrapper">
              <text class="card-icon">📊</text>
            </view>
            <view class="card-info">
              <text class="card-title">战报分析</text>
              <text class="card-desc">AI智能分析游戏表现，提供改进建议</text>
            </view>
            <view class="card-control">
              <switch checked="{{battleReportEnabled}}" bindchange="onBattleReportChange" color="#4CAF50" />
            </view>
          </view>

          <view wx:if="{{battleReportEnabled}}" class="card-features">
            <view class="feature-item">
              <text class="feature-text">📈 详细数据统计</text>
            </view>
            <view class="feature-item">
              <text class="feature-text">🎯 决策质量分析</text>
            </view>
            <view class="feature-item">
              <text class="feature-text">💡 个性化建议</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="setup-row">
      <button type="primary" bindtap="onStartGame">开始游戏</button>
    </view>
  </view>
</view> 
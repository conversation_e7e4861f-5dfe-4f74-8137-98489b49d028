<!--index.wxml-->
<view class="main-container">
  <view class="head">
    <view class="pot_info">
      <view class="pot" data-log="pot"><text class="pot-icon">💰</text>主池: {{gameState.pot}}</view>
      <!-- 鱿鱼模式状态显示 -->
      <view wx:if="{{squidMode}}" class="squid-status">
        <text class="squid-icon">🦑</text>
        <text class="squid-count">{{squidHolders.length}}/{{gameState.players.length - 1}}</text>
      </view>
    </view>
    <view class="phase-indicator">
      <text>{{phaseMap[gameState.gamePhase] || gameState.gamePhase}}</text>
      <text class="phase-timer" wx:if="{{actionCountdown > 0}}"> {{actionCountdown}}s</text>
    </view>

  </view>
  <view class="betting-animation-container">
    <block wx:for="{{bettingAnimations}}" wx:key="key">
      <view class="chip-anim{{item.fromPot ? ' from-pot' : ''}}" style="left:{{item.start.left}}px;top:{{item.start.top}}px;transform:translate({{item.toEnd ? item.end.left-item.start.left : 0}}px,{{item.toEnd ? item.end.top-item.start.top : 0}}px);">
        💰{{item.amount}}
      </view>
      
    </block>
  </view>
  
  <view class="dz-table">
    <!-- 公共牌区 -->
    <view class="community-cards-large horizontal">
      <block wx:for="{{gameState.communityCards}}" wx:key="index">
        <view class="card-large{{communityCardsFlip[index] ? ' flipping' : ''}}">
          <view class="card-large-inner">
            <view class="card-large-front">{{item.value}}{{item.suit}}</view>
            <view class="card-large-back">?</view>
          </view>
        </view>
      </block>
      <!-- 占位符 -->
      <block wx:for="{{5 - gameState.communityCards.length}}" wx:key="index">
        <view class="card-large placeholder">?</view>
      </block>
    </view>
    <!-- 玩家区 -->
    <view class="players-container">
      <block wx:for="{{gameState.players}}" wx:key="id">
        <view class="player{{item.id === gameState.currentPlayerIndex ? ' active-player' : ''}}{{item.folded ? ' folded' : ''}}{{item.eliminated ? ' eliminated' : ''}}" data-player-id="{{item.id + ''}}" data-log="player-{{item.id}}">
          <!-- 鱿鱼图标 - 左上角固定位置 -->
          <view wx:if="{{squidMode && item.hasSquid}}" class="squid-badge">
            <text class="squid-icon">🦑</text>
          </view>
          <view class="player-name-row">
            <text class="player-name">{{item.name}}</text>
            <text class="player-pos">[{{playerPositions[index]}}]</text>
          </view>
          <view class="player-cards">
            <block wx:if="{{item.id === 0}}">
              <view class="card">
                <text class="card-value {{item.hand[0].suit == '♥' || item.hand[0].suit == '♦' ? 'red-suit' : 'black-suit'}}">{{item.hand[0].value}}</text>
                <text class="card-suit {{item.hand[0].suit == '♥' || item.hand[0].suit == '♦' ? 'red-suit' : 'black-suit'}}">{{item.hand[0].suit}}</text>
              </view>
              <view class="card">
                <text class="card-value {{item.hand[1].suit == '♥' || item.hand[1].suit == '♦' ? 'red-suit' : 'black-suit'}}">{{item.hand[1].value}}</text>
                <text class="card-suit {{item.hand[1].suit == '♥' || item.hand[1].suit == '♦' ? 'red-suit' : 'black-suit'}}">{{item.hand[1].suit}}</text>
              </view>
            </block>
            <block wx:else>
              <view class="card-back"></view>
              <view class="card-back"></view>
            </block>
          </view>
          <text class="player-chips">筹码: {{item.chips}}</text>
          <text class="player-bet">该轮下注: {{item.bet}}{{item.isAllIn ? '(全)' : ''}}</text>
          <!-- 状态角标：优先级 淘汰>弃牌>全下 -->
          <view wx:if="{{item.eliminated}}" class="player-status-badge eliminated">
            <text>败</text>
          </view>
          <view wx:elif="{{item.folded}}" class="player-status-badge folded">
            <text>弃</text>
          </view>
          <view wx:elif="{{item.isAllIn}}" class="player-status-badge allin">
            <text>全</text>
          </view>
          <!-- 操作气泡 -->
          <view class="player-action-bubble top-right" wx:if="{{playerActionBubbles['' + item.id]}}">{{playerActionBubbles['' + item.id]}}</view>
        </view>
      </block>
    </view>
    <view wx:if="{{showNextRoundBtn}}" style="text-align:center;margin:24rpx 0;">
      <button type="primary" bindtap="nextRound">下一轮</button>
    </view>

    <view wx:if="{{showNewHandBtn}}" class="end-popup-mask">
      <view class="end-popup">
        <view class="end-popup-title">本局结束</view>
        <view class="end-popup-content">30秒后自动开始下一局</view>
        <view class="end-popup-timer">{{countdown}} 秒</view>
        <button type="primary" bindtap="onManualStartNewHand">开始下一局</button>
      </view>
    </view>

    <!-- 加注弹窗 -->
    <view wx:if="{{showRaiseModal}}" class="modal-mask">
      <view class="modal-content">
        <view class="modal-title">请输入加注金额</view>
        <input class="modal-input" type="number" value="{{raiseInputValue}}" bindinput="onRaiseInputChange" focus="true" placeholder="输入加注筹码" />
        <view class="modal-actions">
          <button class="modal-btn" bindtap="onConfirmRaise">确定</button>
          <button class="modal-btn" bindtap="onHideRaiseModal">取消</button>
        </view>
      </view>
    </view>

    <!-- 结算弹窗 -->
    <view wx:if="{{showEndHandPopup}}" class="modal-mask">
      <view class="modal-content">
        <view class="modal-title">本局结算</view>
        <!-- 新增：公共牌展示 -->
        <view class="modal-community-cards" style="text-align:center;margin-bottom:8px;">
          <block wx:for="{{gameState.communityCards}}" wx:key="index">
            <text class="modal-showdown-card">{{item.value}}{{item.suit}}</text>
          </block>
        </view>
        <view class="modal-result-list">
          <block wx:for="{{endHandResult}}" wx:key="name">
            <view class="modal-result-item">
              <text class="modal-result-name">{{item.name}}</text>
              <text class="modal-result-type">{{item.type}}</text>
              <text class="modal-result-amount">
                <block wx:if="{{item.amount > 0}}">+{{item.amount}}</block>
                <block wx:elif="{{item.amount < 0}}">{{item.amount}}</block>
                <block wx:else>0</block>
              </text>
            </view>
          </block>
        </view>
        <view class="modal-my-result">
          <text wx:if="{{myResult.win}}" style="color:#388e3c;font-weight:bold;">你赢了 {{myResult.amount}} 筹码！</text>
          <text wx:if="{{!myResult.win}}" style="color:#e53935;font-weight:bold;">你输了 {{myResult.amount}} 筹码</text>
        </view>
        <view class="modal-showdown-list" wx:if="{{showdownPlayers && showdownPlayers.length > 0}}">
          <view class="modal-showdown-title">摊牌：</view>
          <block wx:for="{{showdownPlayers}}" wx:key="name">
            <view class="modal-showdown-item">
              <text class="modal-showdown-name{{item.isWinner ? ' winner' : ''}}">{{item.name}}{{item.isWinner ? '🏆' : ''}}</text>
              <block wx:for="{{item.hand}}" wx:key="index">
                <text class="modal-showdown-card">{{item.value}}{{item.suit}}</text>
              </block>
              <!-- 新增：牌型名称 -->
              <text class="modal-showdown-handeval" style="margin-left:8px;color:#388e3c;font-weight:bold;" wx:if="{{item.handEval}}">({{item.handEval}})</text>
            </view>
          </block>
        </view>
        <view class="modal-countdown">{{countdown}} 秒后自动开始下一局</view>
        <button type="primary" style="margin-top:18px;" bindtap="onManualStartNewHand">下一把</button>
      </view>
    </view>

    <view wx:if="{{showChampionPopup}}" class="modal-mask">
      <view class="modal-content">
        <view class="modal-title" style="font-size:40rpx;color:#e6b800;">游戏结束</view>
        <view class="modal-champion" style="font-size:32rpx;margin:32rpx 0 12rpx 0;text-align:center;">冠军：{{championName}}</view>
        <view style="text-align:center;color:#888;">所有筹码已被冠军赢走，游戏结束。</view>
      </view>
    </view>
  </view>

  <!-- 玩家区和AI建议栏等内容 -->
  <view class="controls-round {{gameState.currentPlayerIndex === 0 ? 'breath-glow-red' : ''}}">
    <view class="my-cards-large">
      <view class="my-cards-header my-cards-flex">
        <!-- 左侧：我的手牌 -->
        <view class="my-cards-left">
          <view class="my-cards-cards">
            <block wx:for="{{gameState.players}}" wx:key="id">
              <block wx:if="{{item.id === 0}}">
                <block wx:for="{{item.hand}}" wx:key="index">
                  <view class="card card-large">
                    <text class="card-value {{item.suit == '♥' || item.suit == '♦' ? 'red-suit' : 'black-suit'}}">{{item.value}}</text>
                    <text class="card-suit {{item.suit == '♥' || item.suit == '♦' ? 'red-suit' : 'black-suit'}}">{{item.suit}}</text>
                  </view>
                </block>
              </block>
            </block>
          </view>
        </view>
        <!-- 右侧：AI建议栏和筹码 -->
        <view class="my-cards-right">

          <view class="my-cards-chips-row">
            <text class="my-cards-chips-inline">筹码：{{gameState.players[0].chips}}</text>
            <text class="my-cards-chips-inline" style="margin-left: 1em;">本局投入：{{gameState.players[0].bet}}</text>
          </view>
        </view>
      </view>
    </view>
    <view class="controls-container">
      <!-- 混合操作栏：功能按钮 + 游戏操作按钮在同一行 -->
      <view class="controls-mixed-row">
        <!-- 左侧功能按钮 -->
        <view class="feature-buttons-left">
          <view class="feature-btn" wx:if="{{teachingMode}}" bindtap="toggleTeachingPanel"
                data-tooltip="学习导师">
            <text class="feature-icon">🎓</text>
            <view wx:if="{{teachingHints.length > 0}}" class="hint-badge">{{teachingHints.length}}</view>
          </view>

        </view>

        <!-- 中间游戏操作按钮 -->
        <view class="controls-btns-center">
          <view class="round-btn{{gameState.currentPlayerIndex === 0 ? ' active-player' : ''}}{{gameState.currentPlayerIndex !== 0 || gameState.players[0].bet !== gameState.currentBet ? ' disabled' : ''}}" bindtap="onCheck">看牌</view>
          <view class="round-btn{{gameState.currentPlayerIndex === 0 ? ' active-player' : ''}}{{gameState.currentPlayerIndex !== 0 ? ' disabled' : ''}}" bindtap="onCall">跟注</view>
          <view class="round-btn{{gameState.currentPlayerIndex === 0 ? ' active-player' : ''}}{{gameState.currentPlayerIndex !== 0 ? ' disabled' : ''}}" bindtap="onRaise">加注</view>
          <view class="round-btn{{gameState.currentPlayerIndex === 0 ? ' active-player' : ''}}{{gameState.currentPlayerIndex !== 0 ? ' disabled' : ''}}" bindtap="onFold">弃牌</view>
          <view class="round-btn{{gameState.currentPlayerIndex === 0 ? ' active-player' : ''}}{{gameState.currentPlayerIndex !== 0 ? ' disabled' : ''}}" bindtap="onAllIn">全下</view>
        </view>

        <!-- 右侧预留空间（保持平衡）-->
        <view class="feature-buttons-right">
        </view>
      </view>
    </view>
  </view>

  <view wx:if="{{showNextRoundBtn}}" style="text-align:center;margin:24rpx 0;">
    <button type="primary" bindtap="nextRound">下一轮</button>
  </view>

  <view wx:if="{{showNewHandBtn}}" class="end-popup-mask">
    <view class="end-popup">
      <view class="end-popup-title">本局结束</view>
      <view class="end-popup-content">30秒后自动开始下一局</view>
      <view class="end-popup-timer">{{countdown}} 秒</view>
      <button type="primary" bindtap="onManualStartNewHand">开始下一局</button>
    </view>
  </view>

  <!-- 加注弹窗 -->
  <view wx:if="{{showRaiseModal}}" class="modal-mask">
    <view class="modal-content">
      <view class="modal-title">请输入加注金额</view>
      <input class="modal-input" type="number" value="{{raiseInputValue}}" bindinput="onRaiseInputChange" focus="true" placeholder="输入加注筹码" />
      <view class="modal-actions">
        <button class="modal-btn" bindtap="onConfirmRaise">确定</button>
        <button class="modal-btn" bindtap="onHideRaiseModal">取消</button>
      </view>
    </view>
  </view>

  <view wx:if="{{showChampionPopup}}" class="modal-mask">
    <view class="modal-content">
      <view class="modal-title" style="font-size:40rpx;color:#e6b800;">游戏结束</view>
      <view class="modal-champion" style="font-size:32rpx;margin:32rpx 0 12rpx 0;text-align:center;">冠军：{{championName}}</view>
      <view style="text-align:center;color:#888;">所有筹码已被冠军赢走，游戏结束。</view>
    </view>
  </view>



  <!-- 教学面板 -->
  <view wx:if="{{showTeachingPanel && teachingMode}}" class="teaching-panel-mask" bindtap="closeTeachingPanel">
    <view class="teaching-panel {{teachingPanelClosing ? 'slide-down' : ''}}" catchtap="preventClose">
      <view class="teaching-header">
        <text class="teaching-title">🎓 学习导师</text>
        <view class="teaching-subtitle">帮助你理解游戏原理和策略</view>
        <view class="teaching-close" bindtap="closeTeachingPanel">×</view>
      </view>



    <view class="teaching-content">
      <!-- Loading状态 -->
      <view wx:if="{{teachingLoading}}" class="teaching-loading">
        <view class="loading-header">
          <text class="loading-icon">🎓</text>
          <text class="loading-title">智能分析中...</text>
        </view>
        <view class="loading-content">
          <view class="loading-item">
            <text class="loading-dot">●</text>
            <text class="loading-text">正在深度分析当前局面...</text>
          </view>
          <view class="loading-item">
            <text class="loading-dot">●</text>
            <text class="loading-text">计算手牌强度和胜率...</text>
          </view>
          <view class="loading-item">
            <text class="loading-dot">●</text>
            <text class="loading-text">分析位置优势和底池赔率...</text>
          </view>
          <view class="loading-item">
            <text class="loading-dot">●</text>
            <text class="loading-text">制定最优决策建议...</text>
          </view>
        </view>
        <view class="loading-progress">
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{loadingProgress}}%"></view>
          </view>
          <text class="progress-text">正在运用专业扑克理论为您分析... {{loadingProgress}}%</text>
        </view>
      </view>

      <!-- 正常内容显示 -->
      <block wx:elif="{{teachingHints.length > 0 || aiSuggestion}}">
        <!-- 按优先级分组显示 -->
        <view wx:if="{{highPriorityHints.length > 0 || aiSuggestion || aiThinking}}" class="hint-section">
          <view class="section-title">🔥 重要提示</view>

          <!-- AI决策建议作为第一个高优先级提示 -->
          <view wx:if="{{aiSuggestion && !aiThinking}}" class="hint-item high ai-suggestion">
            <view class="hint-header">
              <text class="hint-type">💡</text>
              <text class="hint-title">决策建议</text>
              <text class="hint-confidence">推荐</text>
            </view>
            <view class="hint-content">
              <view class="suggestion-action">
                <text class="action-label">建议:</text>
                <text class="action-value">{{aiSuggestion.action === 'fold' ? '弃牌' : aiSuggestion.action === 'call' ? '跟注' : aiSuggestion.action === 'raise' ? '加注' : aiSuggestion.action === 'check' ? '看牌' : aiSuggestion.action === 'allin' ? '全押' : aiSuggestion.action}}</text>
                <text wx:if="{{aiSuggestion.amount}}" class="action-amount">{{aiSuggestion.amount}}筹码</text>
              </view>
              <view wx:if="{{aiSuggestion.reason}}" class="suggestion-reason">
                <text class="reason-text">{{aiSuggestion.reason}}</text>
              </view>
            </view>
          </view>

          <!-- AI思考中状态 -->
          <view wx:if="{{aiThinking}}" class="hint-item high ai-thinking">
            <view class="hint-header">
              <text class="hint-type">🤔</text>
              <text class="hint-title">分析中...</text>
            </view>
            <view class="hint-content">
              <text class="thinking-text">正在分析当前局面，为您提供最佳决策建议...</text>
            </view>
          </view>

          <block wx:for="{{highPriorityHints}}" wx:key="index">
            <view class="hint-item high" data-module="{{item.module}}">
              <view class="hint-header">
                <text class="hint-type">{{item.type === 'suggestion' ? '💡' : item.type === 'warning' ? '⚠️' : item.type === 'explanation' ? '📖' : '💭'}}</text>
                <text class="hint-title">{{item.title}}</text>
                <text wx:if="{{item.confidence}}" class="hint-confidence">可信度 {{item.confidence}}%</text>
              </view>
              <text class="hint-content">{{item.content}}</text>
              <view wx:if="{{item.action}}" class="hint-learning">
                <text class="learning-label">💡 学习要点:</text>
                <text>{{item.action}}</text>
              </view>
            </view>
          </block>
        </view>

        <view wx:if="{{mediumPriorityHints.length > 0}}" class="hint-section">
          <view class="section-title">📚 策略指导</view>
          <block wx:for="{{mediumPriorityHints}}" wx:key="index">
            <view class="hint-item medium" data-module="{{item.module}}">
              <view class="hint-header">
                <text class="hint-type">{{item.type === 'suggestion' ? '💡' : item.type === 'warning' ? '⚠️' : item.type === 'explanation' ? '📖' : '💭'}}</text>
                <text class="hint-title">{{item.title}}</text>
              </view>
              <text class="hint-content">{{item.content}}</text>
            </view>
          </block>
        </view>

        <view wx:if="{{lowPriorityHints.length > 0}}" class="hint-section">
          <view class="section-title">💭 基础知识</view>
          <block wx:for="{{lowPriorityHints}}" wx:key="index">
            <view class="hint-item low" data-module="{{item.module}}">
              <view class="hint-header">
                <text class="hint-type">{{item.type === 'suggestion' ? '💡' : item.type === 'warning' ? '⚠️' : item.type === 'explanation' ? '📖' : '💭'}}</text>
                <text class="hint-title">{{item.title}}</text>
              </view>
              <text class="hint-content">{{item.content}}</text>
            </view>
          </block>
        </view>
      </block>
      <view wx:else class="no-hints">
        <view class="empty-state">
          <text class="empty-icon">🎓</text>
          <text class="empty-title">智能教学助手</text>
          <text class="empty-desc">轮到您行动时，我会分析当前局面并提供最佳策略建议</text>
        </view>
      </view>
    </view>
  </view>
  </view>

  <!-- 自定义游戏结束弹窗 -->
  <view wx:if="{{showGameEndPopup}}" class="game-end-popup-mask">
    <view class="game-end-popup">
      <view class="game-end-header">
        <text class="game-end-title">{{gameEndPopupData.title}}</text>
        <view class="game-end-close" bindtap="onCloseGameEndPopup">×</view>
      </view>

      <view class="game-end-content">
        <view class="game-end-main-text">{{gameEndPopupData.content}}</view>

        <view class="game-end-stats">
          <view class="stat-item">
            <text class="stat-label">最终筹码</text>
            <text class="stat-value {{gameEndPopupData.result === 'win' ? 'win' : 'lose'}}">
              {{gameEndPopupData.finalChips}}
            </text>
          </view>
          <view class="stat-item">
            <text class="stat-label">游戏时长</text>
            <text class="stat-value">{{gameEndPopupData.duration}}</text>
          </view>
        </view>
      </view>

      <view class="game-end-actions">
        <button class="game-end-btn secondary" bindtap="onViewBattleReport" wx:if="{{battleReportEnabled}}">
          查看战报
        </button>
        <button class="game-end-btn primary" bindtap="onRestartGame">
          重新开始
        </button>
      </view>
    </view>
  </view>

</view>

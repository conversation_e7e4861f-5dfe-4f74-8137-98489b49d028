/**
 * 德州扑克游戏样式 - 优化版本
 * 使用CSS变量、减少重复、提高可维护性
 */

/* CSS变量定义 */
page {
  /* 颜色变量 */
  --primary-green: #1a5c2e;
  --secondary-green: #2e7d4f;
  --accent-green: #388e3c;
  --gold: #ffd700;
  --red: #ff3b30;
  --error-red: #e53935;
  --white: #fff;
  --black: #222;
  --gray-light: #ccc;
  --gray-medium: #888;
  --gray-dark: #333;
  
  /* 尺寸变量 */
  --card-width: 38px;
  --card-height: 54px;
  --card-large-width: 47.5px;
  --card-large-height: 67.5px;
  --border-radius: 8px;
  --border-radius-large: 18rpx;
  --spacing-xs: 4rpx;
  --spacing-sm: 8rpx;
  --spacing-md: 16rpx;
  --spacing-lg: 24rpx;
  --spacing-xl: 32rpx;
  
  /* 字体变量 */
  --font-size-xs: 22rpx;
  --font-size-sm: 26rpx;
  --font-size-md: 28rpx;
  --font-size-lg: 30rpx;
  --font-size-xl: 36rpx;
  
  /* 阴影变量 */
  --shadow-light: 0 2rpx 8rpx rgba(0,0,0,0.08);
  --shadow-medium: 0 4rpx 16rpx rgba(0,0,0,0.12);
  --shadow-heavy: 0 8rpx 32rpx rgba(0,0,0,0.18);
  
  /* 基础布局 */
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--primary-green);
}

/* 基础布局 */
.main-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  box-sizing: border-box;
  overflow: hidden;
}

/* 头部区域 */
.head {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 60rpx;
  margin: 12rpx 0 var(--spacing-sm);
  position: relative;
  width: 100%;
  max-width: 750rpx;
  box-sizing: border-box;
  min-height: 112rpx;
  height: 112rpx;
}

/* 游戏桌面 */
.dz-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  background: var(--primary-green);
  position: relative;
}
/* 公共牌区域 */
.community-cards {
  display: flex;
  justify-content: center;
  margin-bottom: 20rpx;
  background: rgba(255,255,255,0.06);
  border-radius: 12rpx;
  padding: var(--spacing-sm) 0;
  box-shadow: var(--shadow-light);
}

.community-cards-large {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-md);
  margin: 0 0 var(--spacing-md);
  position: relative;
  z-index: 1;
}

/* 玩家卡片区域 */
.player-cards {
  display: flex;
  justify-content: center;
  margin: var(--spacing-sm) 0;
  gap: var(--spacing-xs);
  padding: 0 12rpx;
}

/* 卡片基础样式 */
.card {
  width: var(--card-width);
  height: var(--card-height);
  background: var(--white);
  border-radius: var(--border-radius);
  margin: 0 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  color: var(--black);
  box-shadow: var(--shadow-light);
  border: 2px solid var(--white);
  box-sizing: border-box;
}

.card-large {
  width: var(--card-large-width);
  height: var(--card-large-height);
  background: var(--white);
  border-radius: var(--border-radius);
  margin: 0 1.33px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  color: var(--black);
  box-shadow: var(--shadow-light);
  border: 2px solid var(--white);
  box-sizing: border-box;
  perspective: 600px;
  position: relative;
}
/* 卡片内容样式 */
.card-value {
  font-size: 15px;
  margin-right: 1px;
}

.card-suit {
  font-size: 12px;
}

.red-suit {
  color: #c62828;
}

.black-suit {
  color: var(--black);
}
.card.flip-animate {
  animation: flipCard 0.6s;
}
@keyframes flipCard {
  0% { transform: rotateY(0deg); }
  50% { transform: rotateY(90deg); }
  100% { transform: rotateY(0deg); }
}
.card.placeholder {
  background: #eee;
  color: #bbb;
}
/* 底池和阶段指示器 */
.pot, .phase-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(30,30,30,0.85);
  border-radius: var(--border-radius-large);
  padding: 0 40rpx;
  min-width: 200rpx;
  height: 112rpx;
  box-shadow: var(--shadow-light);
  color: var(--gold);
  font-weight: bold;
  font-size: var(--font-size-lg);
  text-shadow: 0 2rpx 8rpx var(--gray-dark);
}

.pot {
  max-width: 260rpx;
}

.phase-indicator {
  margin-top: 0;
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  max-width: 160rpx;
}
/* 玩家区域 */
.players-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0 var(--spacing-lg);
  min-height: 260rpx;
  max-height: 800rpx;
  overflow-y: visible;
}

.player {
  background: var(--secondary-green);
  color: var(--white);
  border-radius: 32rpx;
  margin: var(--spacing-xs);
  padding: 11rpx 20rpx;
  min-width: 160rpx;
  max-width: 240rpx;
  width: 200rpx;
  height: 280rpx;
  box-shadow: var(--shadow-light);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: visible;
  box-sizing: border-box;
}

/* 玩家状态 */
.player.folded {
  background: var(--gray-medium);
  color: var(--gray-light);
  opacity: 0.7;
}

.player.eliminated {
  background: var(--black) !important;
  color: var(--gray-light) !important;
  opacity: 0.6;
  filter: grayscale(1);
}

.player.active-player {
  box-shadow: 0 0 0 4rpx var(--red), 0 0 16rpx 8rpx rgba(255,59,48,0.67);
  animation: breath-glow-active-player 1.2s infinite ease-in-out;
  z-index: 2;
  position: relative;
}

.player.active-player::after {
  content: '思考中...';
  position: absolute;
  bottom: -40rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 59, 48, 0.9);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: bold;
  white-space: nowrap;
  animation: thinking-pulse 1.5s infinite ease-in-out;
  box-shadow: 0 4rpx 12rpx rgba(255, 59, 48, 0.3);
}
.player-cards-large {
  display: flex;
  justify-content: center;
  margin: 8rpx 0 24rpx 0;
  gap: 12rpx;
}
.card-large.placeholder {
  background: #eee;
  color: #bbb;
}
/* 卡片背面 */
.card-back {
  width: var(--card-width);
  height: var(--card-height);
  background: repeating-linear-gradient(135deg, var(--secondary-green) 0 8rpx, var(--accent-green) 8rpx 16rpx);
  border-radius: var(--border-radius);
  margin: 0 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  color: var(--white);
  box-shadow: var(--shadow-light);
  border: 0.6px solid var(--gray-light);
  box-sizing: border-box;
}
/* 控制按钮区域 */
.controls {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 12rpx;
  margin-top: var(--spacing-md);
}

.controls button, .controls input {
  margin: var(--spacing-xs);
  border-radius: var(--border-radius);
  transition: background 0.2s, color 0.2s;
  font-size: var(--font-size-md);
  min-width: 80rpx;
  min-height: 48rpx;
  box-shadow: var(--shadow-light);
}

.controls button:active {
  background: var(--accent-green);
  color: var(--gold);
}

.controls button[disabled] {
  background: var(--gray-light);
  color: var(--gray-medium);
}
/* AI思考动画 */
.ai-thinking {
  text-align: center;
  color: var(--gray-medium);
  font-size: var(--font-size-md);
  margin: 12rpx 0;
  animation: ai-blink 1.2s infinite alternate;
}

/* 筹码动画 */
.betting-animation-container {
  position: fixed;
  left: 0; top: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: 10001;
}

.chip-anim {
  position: absolute;
  font-size: var(--font-size-md);
  font-weight: bold;
  color: var(--gold);
  text-shadow: 0 2rpx 8rpx var(--gray-dark);
  transition: transform 0.8s cubic-bezier(.4,2,.6,1);
  will-change: transform;
  pointer-events: none;
}
.my-info {
  text-align: center;
  color: #fff;
  font-size: 28rpx;
  margin: 12rpx 0 8rpx 0;
  position: static;
  left: auto;
  right: auto;
  bottom: auto;
  z-index: 11;
  background: rgba(26,92,46,0.98);
  padding: 4rpx 0;
}
.my-info-inline {
  text-align: center;
  color: #fff;
  font-size: 28rpx;
  margin: 0 0 18rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  background: none;
  padding: 4rpx 0 0 0;
}
/* 控制按钮区域 */
.controls-round {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(255,255,255,0.06);
  border-radius: var(--border-radius-large);
  box-shadow: 0 -4rpx 24rpx rgba(0,0,0,0.10);
  padding: var(--spacing-sm) 0 calc(var(--spacing-sm) + env(safe-area-inset-bottom));
  z-index: 10;
}

.controls-round.breath-glow-red {
  border: 4px solid var(--red);
  animation: breath-glow-red 1.2s infinite alternate;
  box-sizing: border-box;
}

/* 操作区域容器 */
.controls-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

/* 混合行布局 */
.controls-mixed-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 var(--spacing-xl);
  gap: var(--spacing-md);
}

/* 左侧功能按钮 */
.feature-buttons-left {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
  flex: 0 0 auto;
  min-width: 160rpx;
  justify-content: flex-start;
}

/* 中间操作按钮 */
.controls-btns-center {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  gap: var(--spacing-md);
  flex: 1;
  max-width: 500rpx;
  margin: 0 var(--spacing-md);
}

/* 右侧预留空间 */
.feature-buttons-right {
  flex: 0 0 auto;
  min-width: 160rpx;
}

/* 优化的游戏操作按钮样式 */
.round-btn {
  width: 80rpx !important;
  height: 80rpx !important;
  border-radius: 50% !important;
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%) !important;
  color: #ffffff !important;
  font-size: 24rpx !important;
  font-weight: bold !important;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.15), 0 2rpx 6rpx rgba(0, 0, 0, 0.1) !important;
  border: 2rpx solid rgba(255, 255, 255, 0.2) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  padding: 0 !important;
  margin: 0 !important;
  line-height: normal !important;
  min-width: 80rpx !important;
  min-height: 80rpx !important;
  max-width: 80rpx !important;
  max-height: 80rpx !important;
  box-sizing: border-box !important;
  cursor: pointer !important;
  position: relative !important;
  overflow: hidden !important;
}

/* 更具体的选择器 */
.controls-btns-center .round-btn {
  width: 80rpx !important;
  height: 80rpx !important;
  min-width: 80rpx !important;
  min-height: 80rpx !important;
  max-width: 80rpx !important;
  max-height: 80rpx !important;
}

/* 按钮悬停效果 */
.round-btn::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%) !important;
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
  border-radius: 50% !important;
}

/* 按钮激活状态 */
.round-btn:active {
  transform: scale(0.95) !important;
  box-shadow: 0 3rpx 10rpx rgba(0, 0, 0, 0.2), 0 1rpx 3rpx rgba(0, 0, 0, 0.15) !important;
}

.round-btn:active::before {
  opacity: 1 !important;
}

/* 当前玩家激活状态 */
.round-btn.active-player {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%) !important;
  color: #2E7D32 !important;
  border-color: rgba(255, 255, 255, 0.4) !important;
  box-shadow: 0 8rpx 25rpx rgba(255, 215, 0, 0.3), 0 3rpx 8rpx rgba(0, 0, 0, 0.1) !important;
  animation: pulse-glow 2s infinite alternate !important;
}

/* 禁用状态 */
.round-btn.disabled {
  background: linear-gradient(135deg, #9E9E9E 0%, #757575 100%) !important;
  color: #BDBDBD !important;
  opacity: 0.6 !important;
  pointer-events: none !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
}

/* 脉冲发光动画 */
@keyframes pulse-glow {
  0% {
    box-shadow: 0 8rpx 25rpx rgba(255, 215, 0, 0.3), 0 3rpx 8rpx rgba(0, 0, 0, 0.1);
  }
  100% {
    box-shadow: 0 12rpx 35rpx rgba(255, 215, 0, 0.5), 0 5rpx 12rpx rgba(0, 0, 0, 0.15);
  }
}
/* 公共牌区域优化 */
.community-cards-large.horizontal {
  flex-direction: row;
  gap: var(--spacing-md);
}

.community-cards-large,
.community-cards-large.horizontal {
  margin: 1rpx 0 var(--spacing-md);
}
/* 结束弹窗 */
.end-popup-mask {
  position: fixed;
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(0,0,0,0.45);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.end-popup {
  background: var(--white);
  border-radius: var(--border-radius-large);
  padding: 48rpx var(--spacing-xl) var(--spacing-xl);
  min-width: 420rpx;
  box-shadow: var(--shadow-heavy);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.end-popup-title {
  font-size: var(--font-size-xl);
  color: var(--primary-green);
  font-weight: bold;
  margin-bottom: var(--spacing-md);
}

.end-popup-content {
  font-size: var(--font-size-md);
  color: var(--gray-dark);
  margin-bottom: 12rpx;
}

.end-popup-timer {
  font-size: 40rpx;
  color: var(--red);
  font-weight: bold;
  margin-bottom: var(--spacing-lg);
}

.end-popup button {
  width: 260rpx;
  height: 72rpx;
  font-size: var(--font-size-lg);
  border-radius: 12rpx;
  background: var(--primary-green);
  color: var(--white);
  font-weight: bold;
}
/* 我的手牌区域 */
.my-cards-large {
  margin: var(--spacing-md) 0 var(--spacing-sm);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xs) 0;
  height: 120px;
}
/* 我的手牌布局 */
.my-cards-header {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  gap: var(--spacing-lg);
  min-height: 90px;
}

.my-cards-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  text-align: left;
}

.my-cards-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  text-align: right;
  gap: var(--spacing-sm);
}

.my-cards-cards {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.my-cards-chips-inline {
  font-size: 32rpx;
  color: var(--white);
  font-weight: bold;
  white-space: nowrap;
}
/* 卡片翻转动画 */
/* 卡片翻转动画 */
.card-large-inner {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0; top: 0;
  transition: transform 0.6s cubic-bezier(0.4,2,0.2,1);
  transform-style: preserve-3d;
}

.card-large.flipping .card-large-inner {
  transform: rotateY(180deg);
}

.card-large-front, .card-large-back {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0; top: 0;
  backface-visibility: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-large-back {
  background: var(--gray-light);
  color: var(--white);
  transform: rotateY(180deg);
  font-size: 22px;
  font-weight: bold;
}
.card-highlight {
  background: #fff;
  color: #d40000;
  font-weight: bold;
  border: 3rpx solid #ffd700;
  animation: pulse 1.5s infinite;
}
@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.7); }
  70% { box-shadow: 0 0 0 10rpx rgba(255, 215, 0, 0); }
  100% { box-shadow: 0 0 0 0 rgba(255, 215, 0, 0); }
}
.my-cards-chips {
  font-size: 32rpx;
  color: #fff;
  font-weight: bold;
  margin-top: 8rpx;
}
.my-cards-backup {
  margin-top: 16rpx;
  padding: 8rpx 16rpx;
  background: rgba(0,0,0,0.5);
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #ffd700;
  font-weight: bold;
}
.modal-mask, .modal-content, .end-popup-mask, .end-popup {
  z-index: 12000 !important;
}
/* 模态框 */
.modal-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.35);
  z-index: 8000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background: var(--white);
  border-radius: 12px;
  padding: var(--spacing-lg) 20px var(--spacing-md);
  min-width: 220px;
  box-shadow: var(--shadow-heavy);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.modal-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: var(--spacing-md);
}

.modal-input {
  width: 120px;
  height: 32px;
  border: 1px solid var(--gray-light);
  border-radius: 6px;
  font-size: 16px;
  padding: 0 var(--spacing-sm);
  margin-bottom: 18px;
  text-align: center;
}

.modal-actions {
  display: flex;
  gap: var(--spacing-md);
}

.modal-btn {
  min-width: 60px;
  height: 32px;
  border-radius: 6px;
  background: var(--gold);
  color: var(--black);
  font-size: 15px;
  font-weight: bold;
  border: none;
}
/* 模态框结果显示 */
.modal-result-list {
  margin: 12px 0 8px 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.modal-result-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 15px;
  margin-bottom: 4px;
}

.modal-result-name {
  font-weight: bold;
  color: var(--black);
}

.modal-result-type {
  color: var(--gray-medium);
  font-size: 13px;
}

.modal-result-amount {
  color: var(--error-red);
  font-weight: bold;
  margin-left: 4px;
}

.modal-countdown {
  margin-top: 10px;
  color: #666;
  font-size: 13px;
}

/* 摊牌显示 */
.modal-showdown-list {
  margin: 10px 0 6px 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.modal-showdown-title {
  font-size: 14px;
  color: var(--gray-medium);
  margin-bottom: 2px;
  font-weight: bold;
}

.modal-showdown-item {
  display: flex;
  align-items: center;
  font-size: 15px;
  margin-bottom: 2px;
}

.modal-showdown-name {
  color: var(--black);
  font-weight: bold;
  margin-right: 4px;
}

.modal-showdown-name.winner {
  color: var(--error-red);
  font-weight: bold;
  font-size: 16px;
  text-shadow: 0 2px 8px var(--gold);
}

.modal-showdown-card {
  display: inline-block;
  background: var(--white);
  border-radius: 4px;
  border: 1px solid var(--gray-light);
  padding: 2px 6px;
  margin-right: 4px;
  font-size: 14px;
  color: var(--gray-dark);
  font-weight: bold;
}
/* 玩家信息 */
.player-name-row {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 2rpx;
}

.player-name {
  margin-right: var(--spacing-xs);
  font-size: var(--spacing-lg);
  font-weight: bold;
}

.player-pos {
  margin-left: 2rpx;
  font-size: 13px;
  color: var(--gold);
  font-weight: normal;
}

.player-chips, .player-bet {
  font-size: 12px;
  color: #e0e0e0;
  margin-top: 2rpx;
  display: block;
}

.your-turn-breath {
  color: var(--red);
  font-weight: bold;
  margin-left: 18rpx;
  animation: breath-glow-active-player 1.2s infinite alternate;
}
/* 操作气泡 */
.player-action-bubble {
  position: absolute;
  z-index: 9000;
  left: 50%;
  bottom: 100%;
  transform: translateX(-50%) translateY(-2rpx);
  background: rgba(255,255,255,0.3);
  color: var(--black);
  padding: 10rpx var(--spacing-lg);
  border-radius: var(--spacing-lg);
  font-size: var(--font-size-sm);
  font-weight: bold;
  box-shadow: var(--shadow-light);
  min-width: 60rpx;
  text-align: center;
  white-space: nowrap;
  height: 64rpx;
  line-height: 64rpx;
}

.player-action-bubble::after {
  content: '';
  position: absolute;
  left: 28rpx;
  bottom: -16rpx;
  width: 0;
  height: 0;
  border-left: 14rpx solid transparent;
  border-right: 14rpx solid transparent;
  border-top: 16rpx solid rgba(255,255,255,0.3);
  filter: drop-shadow(0 2rpx 4rpx rgba(0,0,0,0.10));
}

.player-action-bubble.top-right {
  left: calc(100% + var(--spacing-sm));
}

.player-action-bubble.folded {
  z-index: 11000;
}

/* 玩家状态徽章 */
.player-status-badge {
  position: absolute;
  top: 0;
  right: 0;
  color: var(--white);
  font-size: var(--font-size-xs);
  font-weight: bold;
  border-radius: 0 12rpx 0 12rpx;
  padding: 2rpx 14rpx;
  z-index: 20;
  box-shadow: var(--shadow-medium);
  letter-spacing: 2rpx;
  white-space: nowrap;
}

.player-status-badge.folded {
  background: var(--gray-medium);
}

.player-status-badge.allin {
  background: var(--error-red);
}

.player-status-badge.eliminated {
  background: var(--black);
  color: var(--gold);
}
/* AI建议栏 */
.ai-suggestion-bar {
  flex: 0 0 12%;
  margin: var(--spacing-lg) auto var(--spacing-xs) auto;
  max-width: 600rpx;
  background: rgba(255,255,255,0.92);
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-light);
  padding: var(--spacing-sm) var(--spacing-md) 6rpx;
  text-align: left;
  color: var(--gray-dark);
  font-size: var(--font-size-xs);
  font-weight: normal;
  max-height: 120rpx;
  overflow-y: auto;
}

.ai-suggestion-title {
  font-size: var(--spacing-lg);
  font-weight: bold;
  color: var(--accent-green);
  margin-bottom: var(--spacing-xs);
}

.ai-suggestion-reason {
  font-size: 20rpx;
  color: #666;
}

.ai-suggestion-bar-inline {
  width: 100%;
  background: none;
  border-radius: 12rpx;
  padding: 6rpx 12rpx 4rpx;
  text-align: center;
  color: var(--gray-dark);
  font-size: 32rpx;
  margin: 2rpx 0 6rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
/* 头部区域细节 */
.pot_info {
  display: flex;
  align-items: center;
  position: absolute;
  left: 60rpx;
  top: 50%;
  transform: translateY(-50%);
  max-width: 300rpx;
  height: 112rpx;
}

.pot-icon, .phase-timer {
  font-size: var(--font-size-lg);
  vertical-align: middle;
  margin: 0 var(--spacing-sm);
}

.phase-indicator text {
  margin: 0;
  padding: 0;
  line-height: 1;
}

/* AI建议栏行布局 */
.ai-suggestion-bar-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-lg);
  color: var(--white);
}

.ai-suggestion-view-reason {
  color: var(--white);
  cursor: pointer;
  font-weight: bold;
}

.ai-suggestion-view-link {
  color: #1976d2;
  text-decoration: underline;
  margin-left: var(--spacing-xs);
}

/* AI建议内容样式 */
.suggestion-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.suggestion-label {
  font-weight: bold;
  color: #FFD700;
  font-size: 24rpx;
}

.suggestion-action {
  color: var(--white);
  font-size: 26rpx;
  font-weight: 500;
}

/* 模态框原因内容 */
.modal-reason-content {
  font-size: var(--font-size-md);
  color: var(--gray-dark);
  margin: var(--spacing-lg) 0 var(--spacing-xl) 0;
  white-space: pre-line;
  text-align: left;
}

/* 我的手牌布局优化 */
.my-cards-flex {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  width: 100%;
}

/* 筹码显示优化 */
.my-cards-chips-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: nowrap;
  white-space: nowrap;
  padding: 6rpx 12rpx 4rpx;
}

.my-cards-chips-row text {
  font-size: 32rpx;
  white-space: nowrap;
}
/* 动画定义 */
@keyframes ai-blink {
  from { opacity: 0.5; }
  to { opacity: 1; }
}

@keyframes breath-glow-active-player {
  0%, 100% {
    box-shadow: 0 0 0 4rpx var(--red), 0 0 16rpx 8rpx rgba(255,59,48,0.67);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 0 6rpx var(--red), 0 0 24rpx 12rpx rgba(255,59,48,0.8);
    transform: scale(1.02);
  }
}

@keyframes thinking-pulse {
  0%, 100% {
    opacity: 0.8;
    transform: translateX(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) scale(1.05);
  }
}

@keyframes chip-move {
  0% { opacity: 0.7; transform: scale(1) translateY(0); }
  60% { opacity: 1; transform: scale(1.2) translateY(-30rpx); }
  100% { opacity: 0; transform: scale(0.7) translateY(60rpx); }
}

/* 决策评分显示 */
.decision-score-bar {
  color: var(--white) !important;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: nowrap;
  white-space: nowrap;
  gap: 1em;
}

.decision-score-view-reason {
  color: var(--white);
  cursor: pointer;
  font-weight: bold;
  white-space: nowrap;
}

.decision-score-view-link {
  color: #1976d2;
  text-decoration: underline;
  margin-left: var(--spacing-xs);
}

/* 我的结果显示 */
.modal-my-result {
  margin: var(--spacing-md) 0;
  text-align: center;
}

.modal-community-cards {
  text-align: center;
  margin-bottom: var(--spacing-sm);
}

.modal-showdown-handeval {
  margin-left: var(--spacing-sm);
  color: var(--accent-green);
  font-weight: bold;
}

.modal-champion {
  font-size: 32rpx;
  margin: var(--spacing-xl) 0 12rpx 0;
  text-align: center;
}

/* 响应式设计 - 微信小程序标准宽度750rpx */
.head {

  max-width: 750rpx;
  width: 100%;
}

/* 小屏幕适配 */
@media screen and (max-width: 750rpx) {
  .head {
    padding: 0 30rpx;
  }

  .pot_info {
    left: 20rpx;
    max-width: 240rpx;
  }

  .phase-indicator {
    right: 20rpx;
    max-width: 140rpx;
  }

  .squid-status {
    margin-left: 8rpx;
    padding: 6rpx 10rpx;
  }

  .squid-icon {
    font-size: 32rpx;
  }

  .squid-count {
    font-size: 24rpx;
  }


  
  .player {
    min-width: 140rpx;
    max-width: 200rpx;
    width: 180rpx;
  }
  
  .controls-mixed-row {
    padding: 0 var(--spacing-lg);
    gap: var(--spacing-sm);
  }

  .feature-buttons-left {
    gap: var(--spacing-sm);
    min-width: 150rpx;
  }

  .controls-btns-center {
    gap: var(--spacing-sm);
    max-width: 400rpx;
    margin: 0 var(--spacing-sm);
  }

  .round-btn {
    width: 68rpx !important;
    height: 68rpx !important;
    min-width: 68rpx !important;
    min-height: 68rpx !important;
    max-width: 68rpx !important;
    max-height: 68rpx !important;
    font-size: 20rpx !important;
  }

  .controls-btns-center .round-btn {
    width: 68rpx !important;
    height: 68rpx !important;
    min-width: 68rpx !important;
    min-height: 68rpx !important;
    max-width: 68rpx !important;
    max-height: 68rpx !important;
  }


  .feature-icon {
    font-size: 28rpx;
  }

  .feature-buttons-right {
    min-width: 150rpx;
  }
  
  .pot, .phase-indicator {
    min-width: 160rpx;
    padding: 0 var(--spacing-lg);
  }
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.font-bold { font-weight: bold; }
.opacity-50 { opacity: 0.5; }
.opacity-70 { opacity: 0.7; }
.hidden { display: none; }
.visible { display: block; }
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.w-full { width: 100%; }
.h-full { height: 100%; }

/* ==========================================
 * 鱿鱼模式样式
 * ========================================== */

/* 头部鱿鱼状态显示 */
.squid-status {
  display: flex;
  align-items: center;
  gap: 6rpx;
  margin-left: 12rpx;
  padding: 6rpx 10rpx;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 14rpx;
  border: 2rpx solid #ff6b6b;
  white-space: nowrap;
}

.squid-icon {
  font-size: 32rpx;
}

.squid-count {
  font-size: 24rpx;
  font-weight: bold;
  color: #ff6b6b;
}

/* 鱿鱼徽章 - 左上角固定位置 */
.squid-badge {
  position: absolute;
  top: -8rpx;
  left: -8rpx;
  z-index: 10;
  background: rgba(76, 175, 80, 0.9);
  border-radius: 50%;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3rpx solid #4caf50;
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.4);
  animation: squid-glow 2s ease-in-out infinite alternate;
}

.squid-badge .squid-icon {
  font-size: 28rpx;
  line-height: 1;
}

/* 鱿鱼发光动画 */
@keyframes squid-glow {
  0% {
    box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.4);
    transform: scale(1);
  }
  100% {
    box-shadow: 0 6rpx 20rpx rgba(76, 175, 80, 0.6);
    transform: scale(1.05);
  }
}

/* 鱿鱼模式下的特殊效果 - 为没有鱿鱼的玩家添加危险提示 */
.player:not(.has-squid-badge) {

}

/* ==========================================
 * 新功能样式
 * ========================================== */

/* 功能按钮样式 */
.feature-btn {
  width: 72rpx;
  height: 72rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15), 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2rpx solid rgba(102, 126, 234, 0.3);
  position: relative;
  cursor: pointer;

  padding: 8rpx;
  margin-left: 20rpx;
}

.feature-btn:active {
  transform: scale(0.9);
  background: linear-gradient(135deg, var(--accent-green) 0%, #45a049 100%);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2), 0 1rpx 3rpx rgba(0, 0, 0, 0.15);
}

.feature-btn:active .feature-icon {
  color: var(--gold);
  transform: scale(1.1);
}

.feature-icon {
  font-size: 32rpx;
  color: var(--accent-green);
}

/* 功能按钮徽章 */
.hint-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #FF4444;
  color: white;
  font-size: 18rpx;
  font-weight: bold;
  border-radius: 50%;
  min-width: 24rpx;
  height: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4rpx;
  box-shadow: 0 2rpx 6rpx rgba(255, 68, 68, 0.3);
}

/* AI思考状态 */
.feature-btn.thinking {
  animation: thinking-pulse 1.5s infinite;
}

@keyframes thinking-pulse {
  0%, 100% {
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15), 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.3), 0 3rpx 8rpx rgba(102, 126, 234, 0.2);
  }
}

/* 功能按钮禁用状态 */
.feature-btn.disabled {
  background: linear-gradient(135deg, rgba(158, 158, 158, 0.8) 0%, rgba(117, 117, 117, 0.8) 100%) !important;
  opacity: 0.5 !important;
  pointer-events: none !important;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1) !important;
}

.feature-btn.disabled .feature-icon {
  color: #9E9E9E !important;
}

/* 教学面板遮罩 */
.teaching-panel-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 12000;
  display: flex;
  align-items: flex-end;
}

/* 教学面板样式 */
.teaching-panel {
  width: 100%;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20rpx 20rpx 0 0;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.15);
  max-height: 70vh;
  animation: slideUp 0.3s ease-out;
  border-top: 3rpx solid var(--accent-green);
  display: flex;
  flex-direction: column;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.teaching-panel.slide-down {
  animation: slideDown 0.3s ease-in forwards;
}

@keyframes slideDown {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}

.teaching-header {
  display: flex;
  flex-direction: column;
  padding: 25rpx 30rpx 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
  position: relative;
}

.teaching-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--accent-green);
  margin-bottom: 8rpx;
}

.teaching-subtitle {
  font-size: 24rpx;
  color: #666;
  opacity: 0.8;
}

.teaching-close {
  position: absolute;
  top: 20rpx;
  right: 25rpx;
  font-size: 48rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.teaching-close:active {
  background-color: #f0f0f0;
  color: #666;
}

.teaching-content {
  padding: 20rpx 30rpx 30rpx;
  max-height: 50vh;
  overflow-y: auto;
  flex: 1;
}

/* 教学内容分组 */
.hint-section {
  margin-bottom: 30rpx;
}

.hint-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  padding-bottom: 8rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

/* 学习要点样式 */
.hint-learning {
  margin-top: 12rpx;
  padding: 12rpx 16rpx;
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
  border-radius: 12rpx;
  border-left: 4rpx solid var(--accent-green);
}

.learning-label {
  font-weight: bold;
  color: var(--accent-green);
  margin-right: 8rpx;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

.hint-item {
  background-color: #f8f9fa;
  border-radius: 15rpx;
  padding: 25rpx;
  margin-bottom: 15rpx;
  border-left: 6rpx solid #e9ecef;
}

.hint-item.high {
  border-left-color: #dc3545;
  background-color: #fff5f5;
}

.hint-item.medium {
  border-left-color: #ffc107;
  background-color: #fffbf0;
}

.hint-item.low {
  border-left-color: #28a745;
  background-color: #f0fff4;
}

.hint-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.hint-type {
  font-size: 24rpx;
  margin-right: 12rpx;
}

.hint-title {
  flex: 1;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.hint-confidence {
  font-size: 22rpx;
  color: #666;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.hint-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  display: block;
  margin-bottom: 12rpx;
}

.hint-action {
  padding: 12rpx 20rpx;
  background-color: rgba(102, 126, 234, 0.1);
  border-radius: 10rpx;
  border: 2rpx solid rgba(102, 126, 234, 0.2);
}

.hint-action text {
  font-size: 24rpx;
  color: #667eea;
  font-weight: bold;
}

.no-hints {
  text-align: center;
  padding: 60rpx 0;
  color: #999;
  font-size: 28rpx;
}



.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  overflow: hidden;
}

.user-avatar image {
  width: 100%;
  height: 100%;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-nickname {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}

.user-level {
  font-size: 20rpx;
  color: #667eea;
}

.user-stats {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.stat-item {
  font-size: 18rpx;
  color: #666;
}

/* AI推荐决策特殊样式 */
.hint-item.ai-recommendation {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border: 2rpx solid rgba(102, 126, 234, 0.3);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.2);
  border-left-color: #667eea !important;
}

.recommended-action {
  margin-top: 16rpx;
  padding: 16rpx;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.15));
  border-radius: 12rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.3);
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.action-label {
  font-size: 26rpx;
  font-weight: bold;
  color: #667eea;
}

.action-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #764ba2;
  text-transform: uppercase;
}

/* AI建议融入教学内容的样式 */
.hint-item.ai-suggestion {
  background: #fff;
  border: 2rpx solid #667eea;
  border-left: 6rpx solid #667eea;
  color: #333;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.15);
}

.hint-item.ai-suggestion .hint-title {
  color: #667eea;
  font-weight: bold;
}

.hint-item.ai-suggestion .hint-confidence {
  background: #667eea;
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: bold;
}

.suggestion-action {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  flex-wrap: wrap;
  gap: 8rpx;
}

.action-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.action-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #667eea;
  text-transform: uppercase;
}

.action-amount {
  font-size: 28rpx;
  font-weight: bold;
  color: #28a745;
}

.suggestion-reason {
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 12rpx 16rpx;
  margin-top: 12rpx;
  border: 1rpx solid #e9ecef;
}

.reason-label {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.reason-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  font-weight: 400;
}

/* AI思考中样式 */
.hint-item.ai-thinking {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border: 2rpx solid rgba(102, 126, 234, 0.3);
  border-left-color: #667eea;
  animation: thinking-pulse 2s infinite ease-in-out;
}

.hint-item.ai-thinking .hint-title {
  color: #667eea;
}

.hint-item.ai-thinking .hint-type {
  animation: thinking-rotate 2s infinite linear;
}

.thinking-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

@keyframes thinking-rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 教学面板Loading状态样式 */
.teaching-loading {
  padding: 40rpx 20rpx;
  text-align: center;
  background: #fff;
  border-radius: 16rpx;
  margin: 20rpx;
}

.loading-header {
  margin-bottom: 40rpx;
}

.loading-icon {
  font-size: 48rpx;
  display: block;
  margin-bottom: 16rpx;
  animation: loading-bounce 2s infinite ease-in-out;
}

.loading-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.loading-content {
  margin: 40rpx 0;
  text-align: left;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
}

.loading-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  opacity: 0;
  animation: loading-fade-in 0.8s ease-in-out forwards;
}

.loading-item:nth-child(1) { animation-delay: 0.2s; }
.loading-item:nth-child(2) { animation-delay: 0.4s; }
.loading-item:nth-child(3) { animation-delay: 0.6s; }
.loading-item:nth-child(4) { animation-delay: 0.8s; }

.loading-dot {
  font-size: 20rpx;
  color: #667eea;
  margin-right: 16rpx;
  animation: loading-pulse 1.5s infinite ease-in-out;
}

.loading-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  font-weight: 500;
}

.loading-progress {
  margin-top: 40rpx;
}

.progress-bar {
  width: 100%;
  height: 6rpx;
  background: #f0f0f0;
  border-radius: 3rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 3rpx;
  width: 0%;
  transition: width 1s ease-out;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  font-weight: 500;
}

/* Loading动画 */
@keyframes loading-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}

@keyframes loading-fade-in {
  from {
    opacity: 0;
    transform: translateX(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes loading-pulse {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}







@keyframes loading-pulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.01);
  }
}

/* ==========================================
   自定义游戏结束弹窗样式
   ========================================== */

.game-end-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;
}

.game-end-popup {
  width: 600rpx;
  max-width: 90%;
  background: var(--white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-heavy);
  animation: slideUp 0.3s ease-out;
  overflow: hidden;
}

.game-end-header {
  position: relative;
  padding: var(--spacing-xl) var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
  color: var(--white);
  text-align: center;
}

.game-end-title {
  font-size: var(--font-size-xl);
  font-weight: bold;
  display: block;
}

.game-end-close {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-lg);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  transition: all 0.2s ease;
}

.game-end-close:active {
  background: rgba(255, 255, 255, 0.1);
  color: var(--white);
}

.game-end-content {
  padding: var(--spacing-xl) var(--spacing-lg);
}

.game-end-main-text {
  font-size: var(--font-size-lg);
  color: var(--gray-dark);
  text-align: center;
  margin-bottom: var(--spacing-xl);
  line-height: 1.5;
}

.game-end-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: var(--spacing-lg);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--gray-medium);
}

.stat-value {
  font-size: var(--font-size-xl);
  font-weight: bold;
}

.stat-value.win {
  color: var(--accent-green);
}

.stat-value.lose {
  color: var(--error-red);
}

.game-end-actions {
  display: flex;
  gap: var(--spacing-md);
  padding: 0 var(--spacing-lg) var(--spacing-xl);
}

.game-end-btn {
  flex: 1;
  height: 80rpx;
  border-radius: var(--border-radius);
  font-size: var(--font-size-md);
  font-weight: bold;
  border: none;
  transition: all 0.2s ease;
}

.game-end-btn.primary {
  background: var(--accent-green);
  color: var(--white);
}

.game-end-btn.primary:active {
  background: var(--secondary-green);
  transform: scale(0.98);
}

.game-end-btn.secondary {
  background: var(--white);
  color: var(--accent-green);
  border: 2rpx solid var(--accent-green);
}

.game-end-btn.secondary:active {
  background: rgba(56, 142, 60, 0.1);
  transform: scale(0.98);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(100rpx) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

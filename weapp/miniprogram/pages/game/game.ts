/**
 * ==========================================
 * 德州扑克游戏页面 - 主文件
 * ==========================================
 *
 * 功能模块：
 * 1. 类型定义和常量
 * 2. 游戏核心逻辑（洗牌、发牌、判胜等）
 * 3. 玩家操作处理（跟注、加注、弃牌等）
 * 4. AI决策系统
 * 5. 动画系统（筹码飞行动画）
 * 6. UI交互处理
 * 7. 页面生命周期
 *
 * 作者：德州扑克小程序开发团队
 * 最后更新：2025-01-18
 */

// ==========================================
// 导入依赖
// ==========================================

// 扑克牌相关工具函数
import { evaluateHand, compareHands, Card } from '../../utils/pokerHand';

// 游戏逻辑工具函数
import {
  actionHistoryToText,
  resetPlayersForNewHand,
  distributePot,
  getPlayerPositionName,
  getBestHandCards,
  addActionHistory,
  clearPlayersActedForPhase,
  startCountdown,
  resetGameUIState,
  buildLLMPrompt,
  buildScorePrompt,
  buildTeachingPrompt,
  parseLLMDecision,
  parseLLMScore,
  getActionText
} from '../../utils/gameUtils';

// 新增功能管理器
import { StatisticsManager } from '../../utils/statisticsManager';
import { UserManager } from '../../utils/userManager';
import { TeachingManager } from '../../utils/teachingManager';
import { BattleReportManager } from '../../utils/battleReportManager';

// 类型定义
import type {
  Player,
  GameState,
  AllInPlayer,
  SidePot,
  BettingAnimation,
  SquidGameState
} from '../../types/gameTypes';

// 游戏常量
import { PHASE_MAP, AI_MODEL_CONFIGS, GAME_CONSTANTS } from '../../constants/gameConstants';



// ==========================================
// 本地常量定义
// ==========================================
/**
 * 动画防抖集合 - 防止重复触发相同的筹码动画
 */
const _betAnimSet = new Set<string>();

// ==========================================
// 本地工具函数
// ==========================================

/**
 * 获取格式化的时间戳
 * @returns 格式化的时间字符串 HH:mm:ss.SSS
 */
function getTimeStamp(): string {
  const now = new Date();
  const hours = now.getHours().toString().padStart(2, '0');
  const minutes = now.getMinutes().toString().padStart(2, '0');
  const seconds = now.getSeconds().toString().padStart(2, '0');
  const milliseconds = now.getMilliseconds().toString().padStart(3, '0');
  return `${hours}:${minutes}:${seconds}.${milliseconds}`;
}

/**
 * 获取玩家本轮最新操作气泡内容
 * @param gameState 游戏状态
 * @param playerId 玩家ID
 * @returns 操作气泡文本
 */
function getPlayerActionBubbleUtil(gameState: any, playerId: number) {
  const phase = gameState.gamePhase;
  const history = gameState.actionHistory && gameState.actionHistory[phase] ? gameState.actionHistory[phase] : [];
  for (let i = history.length - 1; i >= 0; i--) {
    const act = history[i];
    if (act.playerId === playerId) {
      return getActionText(act.action, act.amount);
    }
  }
  return '';
}

/**
 * 玩家操作气泡接口
 */
interface PlayerActionBubbles {
  [key: string]: string
}



// ==========================================
// 微信小程序Component定义
// ==========================================

Component<any, any, any>({
  // 组件属性
  loadingTimer: null as any, // Loading进度条定时器

  /**
   * 页面数据定义
   */
  data: {
    // 用户信息相关
    motto: 'Hello World',
    userInfo: {
      avatarUrl: '',
      nickName: '',
    },
    hasUserInfo: false,
    canIUseGetUserProfile: wx.canIUse('getUserProfile'),
    canIUseNicknameComp: wx.canIUse('input.type.nickname'),

    // 游戏状态核心数据
    gameState: {
      deck: [] as { suit: string; value: string }[],                    // 牌堆
      players: [] as Player[],                                          // 玩家列表
      communityCards: [] as { suit: string; value: string }[],          // 公共牌
      pot: 0,                                                           // 主池金额
      sidePots: [] as SidePot[],                                        // 边池列表
      allInPlayers: [] as AllInPlayer[],                                // 全下玩家列表
      dealerIndex: 0,                                                   // 庄家位置索引
      smallBlindIndex: 1,                                               // 小盲位置索引
      bigBlindIndex: 2,                                                 // 大盲位置索引
      currentPlayerIndex: 0,                                            // 当前行动玩家索引
      currentBet: 0,                                                    // 当前最高下注
      gamePhase: GAME_CONSTANTS.GAME_PHASES.SETUP,                     // 游戏阶段：setup, preFlop, flop, turn, river, showdown
      betRoundComplete: false,                                          // 下注轮是否完成
      actionHistory: {
        [GAME_CONSTANTS.GAME_PHASES.PRE_FLOP]: [],
        [GAME_CONSTANTS.GAME_PHASES.FLOP]: [],
        [GAME_CONSTANTS.GAME_PHASES.TURN]: [],
        [GAME_CONSTANTS.GAME_PHASES.RIVER]: []
      } as { [key: string]: any[] }, // 操作历史记录
      playersActedByPhase: {} as { [key: string]: number[] },           // 记录每个阶段已行动的玩家
      _earlyWinPlayerId: undefined as number | undefined,               // 提前获胜玩家ID
      lastActedPlayerIndex: undefined as number | undefined,            // 最后行动玩家索引
    } as GameState,

    // 游戏配置参数
    raiseAmount: 0,                                                     // 加注金额
    playerCount: 3,                                                     // 玩家数量
    startingChips: 1000,                                                // 初始筹码
    smallBlind: 20,                                                     // 小盲注
    bigBlind: 40,                                                       // 大盲注

    // UI控制状态
    showNextRoundBtn: false,                                            // 显示下一轮按钮
    showNewHandBtn: false,                                              // 显示新局按钮
    showRaiseModal: false,                                              // 显示加注模态框
    showEndHandPopup: false,                                            // 显示结算弹窗
    showChampionPopup: false,                                           // 显示冠军弹窗
    showAiReasonModal: false,                                           // 显示AI原因模态框
    showScoreReasonModal: false,                                        // 显示评分原因模态框
    showGameEndPopup: false,                                            // 显示游戏结束弹窗
    gameEndPopupData: {                                                 // 游戏结束弹窗数据
      title: '',
      content: '',
      result: '', // 'win' | 'lose'
      finalChips: 0,
      duration: ''
    },

    // 动画和视觉效果
    bettingAnimations: [] as BettingAnimation[],                        // 筹码飞行动画列表
    communityCardsFlip: [] as boolean[],                                // 公共牌翻转状态

    // 倒计时相关
    countdown: 30,                                                      // 结算倒计时
    countdownTimer: null as any,                                        // 结算倒计时器
    actionCountdown: 30,                                                // 行动倒计时
    actionCountdownTimer: null as any,                                  // 行动倒计时器

    // 输入和交互
    raiseInputValue: '',                                                // 加注输入值
    championName: '',                                                   // 冠军名称

    // 结算和显示数据
    endHandResult: [] as { name: string; amount: number; type: string }[], // 结算结果
    showdownPlayers: [] as { name: string; hand: { suit: string; value: string }[] }[], // 摊牌玩家
    playerActionBubbles: {} as PlayerActionBubbles,                     // 玩家操作气泡
    playerPositions: [] as string[],                                    // 玩家位置描述

    // AI相关 (仅保留教学模式需要的)
    aiSuggestion: null as null | { action: string; reason: string; amount?: number }, // AI建议 (教学模式用)
    aiThinking: false,                                                  // AI是否正在思考 (教学模式用)

    // 游戏控制
    gameOver: false,                                                    // 游戏是否结束
    actionLock: false,                                                  // 操作锁定状态
    lastActionBubble: null as any,                                      // 最后操作气泡

    // 阶段映射表（中文显示）
    phaseMap: PHASE_MAP,

    // 鱿鱼模式相关
    squidMode: false,                                                   // 是否启用鱿鱼模式
    squidPenaltyMultiplier: 5,                                          // 惩罚倍数
    squidHolders: [] as number[],                                       // 拥有鱿鱼的玩家ID列表

    // 新增功能相关
    currentGameId: '',                                                  // 当前游戏ID
    teachingMode: false,                                                // 教学模式
    battleReportEnabled: false,                                         // 战报分析功能
    gameStartTime: 0,                                                   // 游戏开始时间
    gameResult: '',                                                     // 游戏结果 (win/lose/draw)
    teachingShownThisHand: false,                                       // 本手牌是否已显示教学
    teachingHints: [] as any[],                                         // 教学提示
    teachingLoading: false,                                             // 教学内容加载中
    loadingProgress: 0,                                                 // 加载进度百分比
    // 按优先级分组的教学提示
    highPriorityHints: [] as any[],                                     // 高优先级提示
    mediumPriorityHints: [] as any[],                                   // 中优先级提示
    lowPriorityHints: [] as any[],                                      // 低优先级提示
    showTeachingPanel: false,                                           // 显示教学面板
    teachingPanelClosing: false,                                        // 教学面板关闭动画
    userProfile: null as any,                                           // 用户资料

  },

  /**
   * ==========================================
   * 页面方法定义
   * ==========================================
   */
  methods: {

    // ==========================================
    // 1. 页面生命周期和基础事件处理
    // ==========================================

    /**
     * 页面跳转到日志页面
     */
    bindViewTap() {
      wx.navigateTo({
        url: '../logs/logs',
      })
    },

    getUserProfile() {
      // 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认，开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
      wx.getUserProfile({
        desc: '展示用户信息', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
        success: (res) => {
          this.setData({
            userInfo: res.userInfo,
            hasUserInfo: true
          })
        }
      })
    },



    // ==========================================
    // 2. 游戏核心逻辑 - 牌堆和基础游戏机制
    // ==========================================

    /**
     * 创建一副新的扑克牌
     * @returns 洗好的牌堆
     */
    createDeck(): Card[] {
      const SUITS = ['♠', '♥', '♦', '♣'];
      const VALUES = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
      const deck: Card[] = [];
      for (const suit of SUITS) {
        for (const value of VALUES) {
          deck.push({ suit, value });
        }
      }
      return this.shuffle(deck);
    },

    /**
     * 洗牌算法（Fisher-Yates）
     * @param array 要洗的数组
     * @returns 洗好的数组
     */
    shuffle(array: any[]): any[] {
      const newArray = array.slice();
      for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        const temp = newArray[i];
        newArray[i] = newArray[j];
        newArray[j] = temp;
      }
      return newArray;
    },
    startGame() {
      // 记录游戏开始时间
      this.setData({
        gameStartTime: Date.now(),
        gameResult: ''
      });

      // 只做一次性初始化
      const playerCount = this.data.playerCount;
      const startingChips = this.data.startingChips;
      const playStyles = ['紧凶', '紧弱', '松凶', '松弱'];
      const players: Player[] = [];
      for (let i = 0; i < playerCount; i++) {
        let playStyle = '紧凶', playStyleLevel = 4;
        if (i === 0) {
          // 真人默认紧凶4星
          playStyle = '紧凶';
          playStyleLevel = 4;
        } else {
          // AI随机分配风格和强度
          playStyle = playStyles[Math.floor(Math.random() * playStyles.length)];
          playStyleLevel = Math.floor(Math.random() * 5) + 1;
        }
        players.push({
          id: i,
          name: i === 0 ? `玩家${i}` : `玩家${i}`,
          chips: startingChips,
          hand: [],
          bet: 0,
          folded: false,
          isAllIn: false,
          isDealer: false,
          isSmallBlind: false,
          isBigBlind: false,
          isAI: i !== 0,
          eliminated: false,
          playStyle,
          playStyleLevel
        });
      }
      this.setData({
        'gameState.players': players
      }, () => {
        // 初始化鱿鱼模式状态
        this.initSquidGameState();

        // 开始游戏记录
        this.startGameRecord();

        this.startNewHand();
      });
    },
    // 统一的玩家行动处理器
    performPlayerAction(action: string, extraLogic?: (gameState: any, player: any) => { valid: boolean; message?: string; amount?: number }, reason = '') {
      if (this.data.actionLock) return false;
      this.setData({ actionLock: true });

      const gameState = this.data.gameState;
      const idx = gameState.currentPlayerIndex;
      const player = gameState.players[idx];

      // 执行额外的验证逻辑
      if (extraLogic) {
        const result = extraLogic(gameState, player);
        if (!result.valid) {
          wx.showToast({ title: result.message || '操作无效', icon: 'none' });
          this.setData({ actionLock: false });
          return false;
        }
        if (result.amount !== undefined) {
          this.setData({ raiseAmount: result.amount });
        }
      }

      // 记录玩家操作（仅记录人类玩家）
      if (!player.isAI) {
        const amount = action === 'raise' ? this.data.raiseAmount :
                     action === 'call' ? gameState.currentBet - player.bet : undefined;
        this.recordPlayerAction(action, amount);

        // 操作后评估决策（教学模式）
        if (this.data.teachingMode) {
          this.evaluatePlayerDecision(action, amount);
        }
      }
      
      // 统一处理行动
      handlePlayerAction({
        that: this,
        action,
        reason,
        extraPlayerUpdate: this.getExtraPlayerUpdate(action),
        afterSetData: () => {
          // 玩家操作完成后的处理
        }
      });
      
      return true;
    },
    
    // 获取不同行动类型的额外更新逻辑
    getExtraPlayerUpdate(action: string) {
      const updates: { [key: string]: (player: any, gameState: any) => void } = {
        call: (p: any, gs: any) => {
          const callAmount = gs.currentBet - p.bet;
          if (callAmount > 0 && p.chips >= callAmount) {
            p.chips -= callAmount;
            p.bet += callAmount;
            gs.pot += callAmount;
            if (p.chips === 0 && !p.isAllIn) {
              p.isAllIn = true;
              if (!gs.allInPlayers) gs.allInPlayers = [];
              gs.allInPlayers.push({ playerId: p.id, allInAmount: p.bet });
            }
          }
        },
        allin: (p: any, gs: any) => {
          const allInAmount = p.chips + p.bet;
          if (allInAmount > gs.currentBet) {
            gs.currentBet = allInAmount;
          }
          gs.pot += p.chips;
          p.bet += p.chips;
          p.chips = 0;
          if (!gs.allInPlayers) gs.allInPlayers = [];
          gs.allInPlayers.push({ playerId: p.id, allInAmount });
        },
        raise: (p: any, gs: any) => {
          const raiseAmount = this.data.raiseAmount;
          const diff = raiseAmount - p.bet;
          if (raiseAmount > gs.currentBet && p.chips >= diff) {
            p.chips -= diff;
            p.bet = raiseAmount;
            gs.pot += diff;
            gs.currentBet = raiseAmount;
            if (!gs.playersActedByPhase) gs.playersActedByPhase = {};
            gs.playersActedByPhase[gs.gamePhase] = [gs.currentPlayerIndex];
          }
        }
      };
      return updates[action] || (() => {});
    },
    
    // 封装：玩家操作后统一推进流程
    handleAfterPlayerAction(idx: number, extra?: () => void) {
      if (this.data.gameOver) return;
      const gameState = this.data.gameState;
      const phase = gameState.gamePhase;
      // 维护已行动玩家
      if (!gameState.playersActedByPhase[phase]) gameState.playersActedByPhase[phase] = [];
      if (!gameState.playersActedByPhase[phase].includes(idx)) {
        gameState.playersActedByPhase[phase].push(idx);
      }

      // 每次玩家行动后更新教学相关数据
      if (this.data.teachingMode) {
        this.updateTeachingDataAfterAction(idx);
      }

      if (extra) extra();
      if (this.checkGameEndOrShowdown()) {
        return;
      }
      if (this.isBetRoundComplete()) {
        this._clearPlayersActedForPhase();
        setTimeout(() => {
          this.nextRound();
        }, 800);
        this.clearActionCountdown();
      } else {
        this.advanceToNextPlayer();
      }
      this.setData({ actionLock: false });
    },
    // 弃牌
    onFold(reason = '') {
      this.performPlayerAction('fold', undefined, reason);
    },
    
    // 看牌
    onCheck(reason = '') {
      this.performPlayerAction('check', (gameState: any, player: any) => {
        if (player.bet !== gameState.currentBet) {
          return { valid: false, message: '当前不能看牌' };
        }
        return { valid: true };
      }, reason);
    },
    // 计算主池和边池
    calculatePots() {
      const gameState = this.data.gameState;
      // 按全下金额从小到大排序
      const allInPlayers = gameState.allInPlayers.slice().sort((a: any, b: any) => a.allInAmount - b.allInAmount);
      let previousThreshold = 0;
      gameState.sidePots = [];
      for (const allInPlayer of allInPlayers) {
        const allInAmount = allInPlayer.allInAmount;
        if (allInAmount > previousThreshold) {
          let sidePotAmount = 0;
          for (const player of gameState.players) {
            if (player.bet > previousThreshold) {
              const contribution = Math.min(player.bet, allInAmount) - previousThreshold;
              sidePotAmount += contribution;
            }
          }
          if (sidePotAmount > 0) {
            gameState.sidePots.push({ threshold: allInAmount, amount: sidePotAmount });
          }
          previousThreshold = allInAmount;
        }
      }
      // 更新主池，移除边池的金额
      let mainPot = gameState.pot;
      for (const sidePot of gameState.sidePots) {
        mainPot -= sidePot.amount;
      }
      gameState.pot = mainPot;
      this.setData({ gameState });
    },
    
    // 全下
    onAllIn(reason = '') {
      this.performPlayerAction('allin', undefined, reason);
    },
    
    // 跟注
    onCall(reason = '') {
      this.performPlayerAction('call', (gameState: any, player: any) => {
        const callAmount = gameState.currentBet - player.bet;
        if (callAmount > 0 && player.chips < callAmount) {
          // 筹码不足时自动all-in，投入所有剩余筹码
          if (player.chips > 0) {
            const allInAmount = player.bet + player.chips;
            gameState.pot += player.chips;
            player.bet = allInAmount;
            player.chips = 0;
            player.isAllIn = true;
            if (!gameState.allInPlayers) gameState.allInPlayers = [];
            gameState.allInPlayers.push({ playerId: player.id, allInAmount });
            console.log(`[${player.name}] 筹码不足跟注，自动all-in，投入金额: ${allInAmount}`);
          }
        }
        return { valid: true };
      }, reason);
    },
    
    // 加注
    onRaise(reason: string = '') {
      // 兼容AI和本地AI传递reason参数
      if (typeof reason === 'string' && reason) {
        // 直接走_doRaise，带上reason
        this._doRaise(reason);
      } else {
        // 用户手动点击，弹出加注输入框
        this.onShowRaiseModal();
      }
    },
    
    _doRaise(reason: string = '') {
      this.performPlayerAction('raise', (gameState: any, player: any) => {
        const raiseAmount = Number(this.data.raiseAmount);
        const diff = raiseAmount - player.bet;

        if (raiseAmount <= gameState.currentBet) {
          return { valid: false, message: '加注金额必须大于当前下注' };
        }

        if (player.chips < diff) {
          // 筹码不足时自动all-in，投入所有剩余筹码
          if (player.chips > 0) {
            const allInAmount = player.bet + player.chips;
            gameState.pot += player.chips;
            player.bet = allInAmount;
            player.chips = 0;
            player.isAllIn = true;
            if (!gameState.allInPlayers) gameState.allInPlayers = [];
            gameState.allInPlayers.push({ playerId: player.id, allInAmount });
            console.log(`[${player.name}] 筹码不足加注，自动all-in，投入金额: ${allInAmount}`);
            // 如果all-in金额大于当前最高注，更新最高注
            if (allInAmount > gameState.currentBet) {
              gameState.currentBet = allInAmount;
            }
            return { valid: true, amount: allInAmount };
          }
          return { valid: false, message: '筹码不足' };
        }

        // 修复：当加注后筹码为0时，也要标记为all-in
        if (player.chips === diff) {
          gameState.pot += diff;
          player.bet = raiseAmount;
          player.chips = 0;
          player.isAllIn = true;
          if (!gameState.allInPlayers) gameState.allInPlayers = [];
          gameState.allInPlayers.push({ playerId: player.id, allInAmount: raiseAmount });
          console.log(`[${player.name}] 加注后筹码为0，标记为all-in，投入金额: ${raiseAmount}`);
          return { valid: true, amount: raiseAmount };
        }

        return { valid: true, amount: raiseAmount };
      }, reason);
    },
    // 阶段倒计时相关
    startActionCountdown() {
      if (this.data.gameOver) return;
      this.clearActionCountdown();
      if (this.data.actionCountdownTimer) {
        clearInterval(this.data.actionCountdownTimer);
      }
      const currentPlayer = this.data.gameState.players[this.data.gameState.currentPlayerIndex];
      const isAI = currentPlayer ? currentPlayer.isAI : false;
      const wait = isAI ? 180 : (this.data.actionWaitSeconds || 5); // AI超时3分钟，真人默认5秒
      startCountdown(this, 'actionCountdown', wait, () => {
        // 超时自动决策
        if (this.data.gameState.currentPlayerIndex === 0) {
          this.onFold('超时自动弃牌');
        } else {
          // AI超时弹窗并跳转到设置页
          wx.showModal({
            title: '对局错误',
            content: 'AI玩家长时间未响应，已退出到设置页面。',
            showCancel: false,
            success: () => {
              wx.redirectTo({ url: '/pages/setup/setup' });
            }
          });
        }
      });
    },
    clearActionCountdown() {
      if (this.data.actionCountdownTimer) {
        clearInterval(this.data.actionCountdownTimer);
        this.setData({ actionCountdownTimer: null });
      }
    },
    // 进入下一个玩家
    advanceToNextPlayer() {
      if (this.data.gameOver) return;
      this.clearActionCountdown();
      const gameState = this.data.gameState;
      const playerCount = gameState.players.length;
      let nextIdx = (gameState.currentPlayerIndex + 1) % playerCount;
      // 顺时针找下一个可行动玩家
      for (let i = 0; i < playerCount; i++) {
        const p = gameState.players[nextIdx];
        if (!p.folded && !p.isAllIn && p.chips > 0) {
          gameState.currentPlayerIndex = nextIdx;
          this.setData({ gameState }, () => {
            const currentPlayer = this.data.gameState.players[nextIdx];
            if (currentPlayer) {
              const pos = getPlayerPositionName(gameState.dealerIndex, nextIdx, gameState.players);
              console.log(`[${getTimeStamp()}][操作日志] ${currentPlayer.name}（${pos}），思考中`);
            }
            if (currentPlayer && currentPlayer.isAI && gameState.gamePhase !== GAME_CONSTANTS.GAME_PHASES.SHOWDOWN) {
              setTimeout(() => { this.aiAction(currentPlayer); }, 500);
            } else if (currentPlayer && !currentPlayer.folded && !currentPlayer.isAI && gameState.gamePhase !== GAME_CONSTANTS.GAME_PHASES.SHOWDOWN) {
                // 轮到人类玩家时，提供教学指导和AI建议
                if (currentPlayer.id === 0) {
                  // 教学模式：提供教学指导
                  if (this.data.teachingMode) {
                    this.providePreActionTeaching(currentPlayer, () => {
                      this.startActionCountdown();
                    });
                  } else {
                    this.startActionCountdown();
                  }
                } else {
                  this.startActionCountdown();
                }
            }
          });
          return;
        }
        nextIdx = (nextIdx + 1) % playerCount;
      }
      // 如果一圈找不到可行动玩家，直接进入下一轮
      this._clearPlayersActedForPhase();
      setTimeout(() => { this.nextRound(); }, 800);
      this.clearActionCountdown();
      this.setData({ actionLock: false });
    },
    // 清空本阶段已行动玩家
    _clearPlayersActedForPhase() {
      const gameState = this.data.gameState;
      clearPlayersActedForPhase(gameState, gameState.gamePhase);
    },
    // 加注金额输入
    onRaiseAmountInput(e: any) {
      this.setData({ raiseAmount: Number(e.detail.value) });
    },
    // 发手牌（带动画）
    dealHands() {
      const gameState = this.data.gameState;
      for (const player of gameState.players) {
        player.hand = [gameState.deck.pop()!, gameState.deck.pop()!] as { suit: string; value: string }[];
      }
      this.setData({ gameState });
      // 动画：让所有玩家手牌翻转
      setTimeout(() => {
        const query = wx.createSelectorQuery().in(this);
        query.selectAll('.player-cards .card').boundingClientRect((cards: any) => {
          if (cards) {
            cards.forEach((_: any, idx: number) => {
              const cardSelector = `.player-cards .card:nth-child(${(idx%2)+1})`;
              wx.createSelectorQuery().in(this).selectAll(cardSelector).fields({ node: true }, (nodes: any) => {
                if (nodes && nodes[0]) {
                  nodes[0].dataset = nodes[0].dataset || {};
                  nodes[0].dataset.flip = true;
                  setTimeout(() => { nodes[0].dataset.flip = false; }, 600);
                }
              }).exec();
            });
          }
        }).exec();
      }, 200);
    },
    // 发公共牌（带动画，支持一张一张慢慢翻，带翻转特效）
    dealCommunityCardsSequentially(count: number, callback?: () => void) {
      const gameState = this.data.gameState;
      const deck = gameState.deck;
      let communityCardsFlip = this.data.communityCardsFlip.slice();
      const revealOne = (left: number) => {
        if (left <= 0 || deck.length === 0) {
          // 调试：动画结束后输出当前communityCards内容
          // 动画全部完成后再打印日志（如果有pendingLogFn）
          if (typeof (this as any)._pendingCommunityLog === 'function') {
            (this as any)._pendingCommunityLog();
            (this as any)._pendingCommunityLog = null;
          }
          if (callback) callback();
          return;
        }
        // 先插入一张背面牌
        gameState.communityCards.push({ suit: '', value: '' });
        communityCardsFlip.push(true); // 先设为翻转状态
        this.setData({ gameState, communityCardsFlip });
        setTimeout(() => {
          // 替换为正面牌
          const realCard = deck.pop() || { suit: '', value: '' };
          gameState.communityCards[gameState.communityCards.length - 1] = realCard;
          // 修复：翻正面时将flip设为false
          communityCardsFlip[gameState.communityCards.length - 1] = false;
          this.setData({ gameState, communityCardsFlip });
          setTimeout(() => revealOne(left - 1), 200);
        }, 300);
      };
      revealOne(count);
    },
    // 进入下一轮
    nextRound() {
      const gameState = this.data.gameState;
      // 新增：只剩一人未弃牌，直接判胜结算
      const notFolded = gameState.players.filter((p: any) => !p.folded);
      if (notFolded.length === 1) {
        console.log('[阶段推进] 只剩一人未弃牌，直接进入结算(showdown)');
        const winner = notFolded[0];
        // 记录结算前筹码
        const chipsBefore: { [id: number]: number } = {};
        gameState.players.forEach((p: any) => { chipsBefore[p.id] = p.chips; });
        // 确保赢家未被淘汰/未弃牌
        winner.folded = false;
        this.setData({ gameState }, () => {
          this.showEndHandPopupWithResult();
        });
        this.setData({ actionLock: false });
        return;
      }
      // 新增：所有未弃牌玩家都all-in，自动补齐剩余公共牌并结算
      const allAllIn = notFolded.length > 1 && notFolded.every((p: any) => p.isAllIn);
      if (allAllIn && gameState.gamePhase !== GAME_CONSTANTS.GAME_PHASES.SHOWDOWN) {
        const left = 5 - gameState.communityCards.length;
        console.log(`[阶段推进] 因为所有未弃牌玩家都已全下，自动补齐剩余公共牌并进入摊牌结算(showdown)`);
        if (left > 0) {
          this.dealCommunityCardsSequentially(left, () => {
            gameState.gamePhase = GAME_CONSTANTS.GAME_PHASES.SHOWDOWN;
            this.setData({ gameState }, () => {
              this.showEndHandPopupWithResult();
            });
          });
        } else {
          gameState.gamePhase = GAME_CONSTANTS.GAME_PHASES.SHOWDOWN;
          this.calculatePots();
          this.setData({ gameState }, () => {
            this.showEndHandPopupWithResult();
          });
        }
        this.setData({ actionLock: false });
        return;
      }
      const doNext = () => {
        // 重置每个玩家的 bet
        gameState.players.forEach((p: any) => { p.bet = 0; });
        // 每轮开始时，重置currentBet为0（除preFlop外）
        if (gameState.gamePhase === GAME_CONSTANTS.GAME_PHASES.FLOP ||
            gameState.gamePhase === GAME_CONSTANTS.GAME_PHASES.TURN ||
            gameState.gamePhase === GAME_CONSTANTS.GAME_PHASES.RIVER) {
          gameState.currentBet = 0;
        }
        // 每轮开始时，清空本阶段已行动玩家
        gameState.playersActedByPhase[gameState.gamePhase] = [];
        // 每轮开始时，确定首位行动玩家
        const playerCount = gameState.players.length;
        let startIdx;
        if (gameState.gamePhase === GAME_CONSTANTS.GAME_PHASES.PRE_FLOP) {
          startIdx = (gameState.bigBlindIndex + 1) % playerCount; // 枪口/UTG
        } else {
          // 翻牌圈及之后：从庄家后面第一个活跃玩家开始
          startIdx = (gameState.dealerIndex + 1) % playerCount;
        }

        // 查找第一个可行动的玩家
        let idx = startIdx;
        let found = false;
        console.log(`[LOG] [nextRound/doNext] 开始查找首位行动玩家，phase=${gameState.gamePhase}, startIdx=${startIdx}`);

        for (let i = 0; i < playerCount; i++) {
          const p = gameState.players[idx];
          console.log(`[LOG] [nextRound/doNext] 检查玩家${idx} ${p.name}: folded=${p.folded}, isAllIn=${p.isAllIn}, chips=${p.chips}, eliminated=${p.eliminated}`);

          if (!p.folded && !p.isAllIn && p.chips > 0 && !p.eliminated) {
            gameState.currentPlayerIndex = idx;
            found = true;
            console.log(`[LOG] [nextRound/doNext] 找到首位行动玩家: ${p.name} (索引${idx})`);
            break;
          }
          idx = (idx + 1) % playerCount;
        }

        if (!found) {
          console.log(`[LOG] [nextRound/doNext] 警告：未找到可行动玩家！`);
        }

        // 日志：所有玩家状态
        gameState.players.forEach((p: any, i: number) => {
          console.log(`[LOG] [nextRound/doNext] 玩家${i} name=${p.name} folded=${p.folded} isAllIn=${p.isAllIn} chips=${p.chips} eliminated=${p.eliminated}`);
        });
        this.setData({ gameState });
        // 统一推进到下一个玩家
        this.advanceToNextPlayer();
      };
      // 修正：每次 nextRound 只推进一个阶段，不连跳
      switch (gameState.gamePhase) {
        case GAME_CONSTANTS.GAME_PHASES.PRE_FLOP: {
          // 进入翻牌圈，动画完成后再打印日志
          (this as any)._pendingCommunityLog = () => {
            gameState.gamePhase = GAME_CONSTANTS.GAME_PHASES.FLOP;
            const communityStr = gameState.communityCards.filter((c: any) => c.suit && c.value).length === 0 ? '无' : gameState.communityCards.filter((c: any) => c.suit && c.value).map((c: any) => c.value + c.suit).join(', ');
            console.log(`[阶段推进] 进入翻牌圈(flop)，公共牌: ${communityStr}`);
            this.setData({ gameState }, () => { doNext(); });
          };
          this.dealCommunityCardsSequentially(3);
          break;
        }
        case GAME_CONSTANTS.GAME_PHASES.FLOP: {
          (this as any)._pendingCommunityLog = () => {
            gameState.gamePhase = GAME_CONSTANTS.GAME_PHASES.TURN;
            const communityStr = gameState.communityCards.filter((c: any) => c.suit && c.value).length === 0 ? '无' : gameState.communityCards.filter((c: any) => c.suit && c.value).map((c: any) => c.value + c.suit).join(', ');
            console.log(`[阶段推进] 进入转牌圈(turn)，公共牌: ${communityStr}`);
            this.setData({ gameState }, () => { doNext(); });
          };
          this.dealCommunityCardsSequentially(1);
          break;
        }
        case GAME_CONSTANTS.GAME_PHASES.TURN: {
          (this as any)._pendingCommunityLog = () => {
            gameState.gamePhase = GAME_CONSTANTS.GAME_PHASES.RIVER;
            const communityStr = gameState.communityCards.filter((c: any) => c.suit && c.value).length === 0 ? '无' : gameState.communityCards.filter((c: any) => c.suit && c.value).map((c: any) => c.value + c.suit).join(', ');
            console.log(`[阶段推进] 进入河牌圈(river)，公共牌: ${communityStr}`);
            this.setData({ gameState }, () => { doNext(); });
          };
          this.dealCommunityCardsSequentially(1);
          break;
        }
        case GAME_CONSTANTS.GAME_PHASES.RIVER: {
          const communityStr = gameState.communityCards.filter((c: any) => c.suit && c.value).length === 0 ? '无' : gameState.communityCards.filter((c: any) => c.suit && c.value).map((c: any) => c.value + c.suit).join(', ');
          console.log(`[阶段推进] 进入摊牌结算(showdown)，公共牌: ${communityStr}`);
            gameState.gamePhase = GAME_CONSTANTS.GAME_PHASES.SHOWDOWN;
            this.calculatePots();
            this.setData({ gameState }, () => {
              this.showEndHandPopupWithResult();
          });
          break;
        }
        default:
          break;
      }
      // 进入下一阶段时清空AI建议
      this.setData({ aiSuggestion: null, aiThinking: false });
    },
    // 新一局
    startNewHand() {
      if (this.data.gameOver) return;

      // 防止多次触发新局
      if ((this as any)._newHandStarted) return;
      (this as any)._newHandStarted = true;
      (this as any)._chipsBeforeSettle = null;

      // 重置教学显示标记
      this.setData({ teachingShownThisHand: false });
      // 清理所有与新局相关的变量
      if (this.data.actionCountdownTimer) {
        clearInterval(this.data.actionCountdownTimer);
        this.setData({ actionCountdownTimer: null });
      }
      if (this.data.countdownTimer) {
        clearInterval(this.data.countdownTimer);
        this.setData({ countdownTimer: null });
      }
      resetGameUIState(this);
      const gameState = this.data.gameState;
      const smallBlind = this.data.smallBlind;
      const bigBlind = this.data.bigBlind;
      const playerCount = gameState.players.length;
      // 新一局庄家轮转：首局随机，后续每局顺序递增（跳过已淘汰玩家）
      if (gameState.dealerIndex === undefined) {
        // 第一局随机
        let firstDealer = Math.floor(Math.random() * playerCount);
        // 跳过已淘汰玩家
        while (gameState.players[firstDealer].chips <= 0) {
          firstDealer = (firstDealer + 1) % playerCount;
        }
        gameState.dealerIndex = firstDealer;
      } else {
        // 后续每局顺序递增，跳过已淘汰玩家
        let nextDealer = gameState.dealerIndex;
        for (let i = 1; i <= playerCount; i++) {
          const idx = (gameState.dealerIndex + i) % playerCount;
          const p = gameState.players[idx];
          if (p.chips > 0) {
            nextDealer = idx;
            break;
          }
        }
        gameState.dealerIndex = nextDealer;
      }
      gameState.smallBlindIndex = (gameState.dealerIndex + 1) % playerCount;
      gameState.bigBlindIndex = (gameState.dealerIndex + 2) % playerCount;
      // 只重置手牌、下注、弃牌等状态，保留筹码
      resetPlayersForNewHand(gameState.players, gameState.dealerIndex, gameState.smallBlindIndex, gameState.bigBlindIndex);
      // 新一局开始时打印所有玩家初始筹码（包括大小盲），放在扣盲前
      console.log(`[${getTimeStamp()}][操作日志] 新一局开始，各位玩家初始筹码如下：`);
      gameState.players.forEach((p: any, i: number) => {
        const pos = getPlayerPositionName(gameState.dealerIndex, i, gameState.players);
        let aiParamStr = '-';
        if (p.isAI) {
          const aiParams = AI_MODEL_CONFIGS[p.id % AI_MODEL_CONFIGS.length];
          aiParamStr = `[model:${aiParams.model}, version:${aiParams.version}, proxy:${aiParams.proxy}]`;
        }
        console.log(`[${getTimeStamp()}][操作日志] ${p.name}（${pos}）：${p.chips} ${aiParamStr}`);
      });
      // 记录每局初始筹码
      (this as any)._startHandChips = gameState.players.map((p: any) => p.chips);
      // 新一局重新洗牌并发手牌
      const deck = this.createDeck();
      gameState.deck = deck; // 关键：赋值回gameState，确保发手牌和发公共牌用同一副牌堆
      for (const player of gameState.players) {
        if (!player.folded) {
          const card1 = deck.pop() || { suit: '', value: '' };
          const card2 = deck.pop() || { suit: '', value: '' };
          player.hand = [card1, card2];
        }
      }
      // 收取盲注（只从未淘汰玩家收取）
      const sbPlayer = gameState.players[gameState.smallBlindIndex];
      const bbPlayer = gameState.players[gameState.bigBlindIndex];
      if (!sbPlayer.folded && sbPlayer.chips >= smallBlind) {
      sbPlayer.chips -= smallBlind;
      sbPlayer.bet = smallBlind;
      } else {
        sbPlayer.bet = 0;
      }
      if (!bbPlayer.folded && bbPlayer.chips >= bigBlind) {
      bbPlayer.chips -= bigBlind;
      bbPlayer.bet = bigBlind;
      } else {
        bbPlayer.bet = 0;
      }
      // 彻底清空所有阶段相关变量
      gameState.actionHistory = { preFlop: [], flop: [], turn: [], river: [] };
      gameState.playersActedByPhase = { preFlop: [], flop: [], turn: [], river: [] };
      gameState.lastActedPlayerIndex = undefined;
      gameState._earlyWinPlayerId = undefined;
      gameState.communityCards = [];
      gameState.pot = (sbPlayer.bet || 0) + (bbPlayer.bet || 0);
      gameState.sidePots = [];
      gameState.allInPlayers = [];
      // 新增：盲注写入actionHistory
      const actionHistory = gameState.actionHistory;
      if (!sbPlayer.folded && sbPlayer.bet > 0) {
        addActionHistory(gameState, GAME_CONSTANTS.GAME_PHASES.PRE_FLOP, {
          playerId: sbPlayer.id,
          playerName: sbPlayer.name,
          action: 'smallBlind',
          amount: sbPlayer.bet
        });
      }
      if (!bbPlayer.folded && bbPlayer.bet > 0) {
        addActionHistory(gameState, GAME_CONSTANTS.GAME_PHASES.PRE_FLOP, {
          playerId: bbPlayer.id,
          playerName: bbPlayer.name,
          action: 'bigBlind',
          amount: bbPlayer.bet
        });
      }
      this.setData({ 'gameState.actionHistory': actionHistory }, () => {
        this.updatePlayerActionBubbles(); // 立即刷新气泡，盲注投入后立刻显示

        // 触发盲注动画
        setTimeout(() => {
          if (sbPlayer.bet > 0) {
            this.animateChips(sbPlayer.id, sbPlayer.bet);
          }
          setTimeout(() => {
            if (bbPlayer.bet > 0) {
              this.animateChips(bbPlayer.id, bbPlayer.bet);
            }
          }, 300); // 大盲动画延迟300ms
        }, 500); // 小盲动画延迟500ms，确保界面已更新
      });
      // 修正：preFlop第一位行动者（枪口/UTG）currentPlayerIndex和actionCursor都指向同一个玩家
      const firstToAct = (gameState.bigBlindIndex + 1) % playerCount;
      gameState.currentPlayerIndex = firstToAct;
      gameState.currentBet = Math.max(sbPlayer.bet, bbPlayer.bet);
      gameState.gamePhase = GAME_CONSTANTS.GAME_PHASES.PRE_FLOP;
      this.setData({ gameState, showEndHandPopup: false, endHandResult: [], showdownPlayers: [], myResult: {} }, () => {
        this.updatePlayerPositions();
        this.startCurrentPlayerAction();
      });
      // 检查游戏结束条件（基于人类玩家状态）
      if (this.checkGameEndCondition()) {
        return;
      }
    },
    // 开始当前玩家行动（不跳到下一个玩家）
    startCurrentPlayerAction() {
      if (this.data.gameOver) return;
      this.clearActionCountdown();
      const gameState = this.data.gameState;
      const currentIdx = gameState.currentPlayerIndex;
      const currentPlayer = gameState.players[currentIdx];
      
      if (!currentPlayer || currentPlayer.folded || currentPlayer.isAllIn || currentPlayer.chips === 0) {
        // 当前玩家不能行动，跳到下一个玩家
        this.advanceToNextPlayer();
        return;
      }
      
      const pos = getPlayerPositionName(gameState.dealerIndex, currentIdx, gameState.players);
      console.log(`[${getTimeStamp()}][操作日志] ${currentPlayer.name}（${pos}），思考中`);
      
      if (currentPlayer.isAI && gameState.gamePhase !== GAME_CONSTANTS.GAME_PHASES.SHOWDOWN) {
        setTimeout(() => { this.aiAction(currentPlayer); }, 500);
      } else if (!currentPlayer.folded && !currentPlayer.isAI && gameState.gamePhase !== GAME_CONSTANTS.GAME_PHASES.SHOWDOWN) {
        // 轮到人类玩家时，提供教学指导和AI建议
        if (currentPlayer.id === 0) {
          // 教学模式：提供教学指导
          if (this.data.teachingMode) {
            this.providePreActionTeaching(currentPlayer, () => {
              this.startActionCountdown();
            });
          } else {
            this.startActionCountdown();
          }
        } else {
          this.startActionCountdown();
        }
      }
    },
    updatePlayerPositions() {
      const gameState = this.data.gameState;
      if (!gameState || !gameState.players) return;
      const positions = gameState.players.map((p: any, i: number) => getPlayerPositionName(gameState.dealerIndex, i, gameState.players));
      this.setData({ playerPositions: positions });
    },
    // 真实牌型判定
    determineWinners() {
      const gameState = this.data.gameState;
      const community = gameState.communityCards;
      const activePlayers = gameState.players.filter((p: any) => !p.folded);
      // 评估每个玩家的最佳牌型
      const hands = activePlayers.map((p: any) => {
        const allCards = p.hand.concat(community);
        const handEval = evaluateHand(allCards);
        return { player: p, handEval };
      });
      // 找到最大牌型
      hands.sort((a: any, b: any) => -compareHands(a.handEval, b.handEval));
      const best = hands[0].handEval;
      const winners = hands.filter((h: any) => compareHands(h.handEval, best) === 0).map((h: any) => h.player);
      return winners;
    },
    // 边池专用胜者判定
    determineWinnersForPlayers(players: any[]) {
      const gameState = this.data.gameState;
      const community = gameState.communityCards;
      const hands = players.map((p: any) => {
        const allCards = p.hand.concat(community);
        const handEval = evaluateHand(allCards);
        return { player: p, handEval };
      });
      hands.sort((a: any, b: any) => -compareHands(a.handEval, b.handEval));
      const best = hands[0].handEval;
      const winners = hands.filter((h: any) => compareHands(h.handEval, best) === 0).map((h: any) => h.player);
      return winners;
    },
    // 通用 LLM 请求工具函数
    requestLLM({ url, prompt, systemPrompt, model, version, proxy, onSuccess, onFail }: {
      url: string;
      prompt: string;
      systemPrompt: string;
      model: string;
      version: string;
      proxy: boolean;
      onSuccess: (res: any) => void;
      onFail: () => void;
    }) {
      wx.request({
        url,
        method: 'POST',
        data: {
          message: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: prompt }
          ],
          model,
          version,
          proxy
        },
        header: { 'Content-Type': 'application/json' },
        success: onSuccess,
        fail: onFail
      });
    },
    
    // 统一的LLM请求管理器
    makeLLMRequest(type: 'decision' | 'suggestion' | 'score' | 'teaching', player: any, additionalData?: any) {
      const callbacks = {
        decision: {
          onSuccess: (content: string) => {
            const { action, amount, reason } = parseLLMDecision(content);
            // 决策前校验，防止AI决策错位
            const currentPlayer = this.data.gameState.players[this.data.gameState.currentPlayerIndex];
            if (!currentPlayer || currentPlayer.id !== player.id) {
              console.warn('[AI决策] 玩家已切换，丢弃本次AI决策', player.id, '当前应行动玩家id:', currentPlayer && currentPlayer.id);
              return;
            }
            
            this.clearActionCountdown();
            this.executeAIDecision(action, reason, amount);
          },
          onFail: () => {
            console.log(`[AI行动] LLM请求失败，降级为simpleAiAction，玩家id=${player.id} name=${player.name}`);
            this.clearActionCountdown();
            this.simpleAiAction(player);
          }
        },
        suggestion: {
          onSuccess: (content: string) => {
            const { action, reason, amount } = parseLLMDecision(content);
            this.setData({
              aiSuggestion: { action, reason, amount },
              aiThinking: false
            }, additionalData && additionalData.callback ? additionalData.callback : undefined);
          },
          onFail: () => {
            this.setData({
              aiSuggestion: null,
              aiThinking: false
            }, additionalData && additionalData.callback ? additionalData.callback : undefined);
          }
        },
        teaching: {
          onSuccess: (content: string) => {
            const teachingData = this.parseTeachingResponse(content);
            if (teachingData && additionalData && additionalData.callback) {
              additionalData.callback(teachingData);
            }
          },
          onFail: () => {
            console.error('[Teaching] AI教学分析失败，使用降级逻辑');
            if (additionalData && additionalData.fallbackCallback) {
              additionalData.fallbackCallback();
            }
          }
        }
      };
      
      const { prompt, systemPrompt } = this.buildPromptForType(type, player, additionalData);
      const aiParams = AI_MODEL_CONFIGS[player.id % AI_MODEL_CONFIGS.length];
      
      this.requestLLM({
        url: 'https://shop.hangjw.com/v3/api/ap/chat',
        prompt,
        systemPrompt,
        model: aiParams.model,
        version: aiParams.version,
        proxy: aiParams.proxy,
        onSuccess: (res: any) => {
          const content = this.extractContentFromResponse(res);
          callbacks[type].onSuccess(content);
        },
        onFail: callbacks[type].onFail
      });
    },
    
    // 构建不同类型的提示词
    buildPromptForType(type: 'decision' | 'suggestion' | 'teaching', player: any, additionalData?: any) {
      switch (type) {
        case 'decision':
        case 'suggestion':
          return buildLLMPrompt({
            gameState: this.data.gameState,
            player,
            phaseMap: this.data.phaseMap,
            getBestHandCards,
            actionHistoryToText
          });
        case 'teaching':
          return buildTeachingPrompt(this.data.gameState, player, this.data.gameState.actionHistory || {});
        default:
          return { prompt: '', systemPrompt: '' };
      }
    },
    
    // 提取响应内容
    extractContentFromResponse(res: any): string {
      return (res && res.data && res.data.data && res.data.data.content) ? res.data.data.content : '';
    },
    
    // 执行AI决策
    executeAIDecision(action: string, reason: string, amount?: number) {
      if (action === 'call') this.onCall(reason);
      else if (action === 'raise') {
        this.setData({ raiseAmount: Number(amount) || 0 }, () => {
          this._doRaise(reason);
        });
      }
      else if (action === 'fold') this.onFold(reason);
      else if (action === 'allin' || action === 'all-in') this.onAllIn(reason);
      else if (action === 'check') this.onCheck(reason);
      else this.onCall(reason);
    },
    // AI决策（调用接口）
    aiAction(player: any) {
      if (this.data.gameOver) return;
      const gameState = this.data.gameState;
      if (player.folded || player.isAllIn || player.chips === 0) return;
      
      this.makeLLMRequest('decision', player);
    },
    // 本地简单AI（降级）
    simpleAiAction(player: any) {
      if (this.data.gameOver) return;
      const gameState = this.data.gameState;
      const allCards = player.hand.concat(gameState.communityCards);
      const handEval = evaluateHand(allCards);
      let strength = handEval.rank + handEval.value[0]/100;
      const callAmount = gameState.currentBet - player.bet;

      // 鱿鱼模式调整：没有鱿鱼的玩家更加激进
      let squidBonus = 0;
      if (this.data.squidMode && !this.hasSquid(player.id)) {
        const activePlayers = gameState.players.filter((p: any) => !p.eliminated);
        const playersWithoutSquid = activePlayers.filter((p: any) => !this.hasSquid(p.id));

        if (playersWithoutSquid.length <= 2) {
          // 危险！只剩1-2个没鱿鱼的玩家，极度激进
          squidBonus = 3.0;
          console.log(`[鱿鱼AI] ${player.name} 处于危险状态，激进度+3.0`);
        } else if (playersWithoutSquid.length <= 3) {
          // 比较危险，增加激进度
          squidBonus = 1.5;
          console.log(`[鱿鱼AI] ${player.name} 需要鱿鱼，激进度+1.5`);
        } else {
          // 轻微增加激进度
          squidBonus = 0.5;
          console.log(`[鱿鱼AI] ${player.name} 没有鱿鱼，激进度+0.5`);
        }
      }

      strength += squidBonus;

      // 日志：simpleAiAction决策前
      console.log(`[AI行动] simpleAiAction被调用，玩家id=${player.id} name=${player.name} 阶段=${gameState.gamePhase} bet=${player.bet} chips=${player.chips} 原始牌力=${handEval.rank + handEval.value[0]/100} 调整后牌力=${strength}`);
      // 决策前清理倒计时
      this.clearActionCountdown();
      if (callAmount === 0) {
        if (strength > 6.5) {
          console.log(`[AI行动] simpleAiAction决策=allin`);
          this.onAllIn('牌力极强，直接全下');
        }
        else if (strength > 4.5) {
          console.log(`[AI行动] simpleAiAction决策=raise`);
          this.onRaise('牌力较强，选择加注');
        }
        else {
          console.log(`[AI行动] simpleAiAction决策=check`);
          this.onCheck('免费看牌，选择check');
        }
      } else {
        if (strength > 6.5) {
          console.log(`[AI行动] simpleAiAction决策=allin`);
          this.onAllIn('牌力极强，直接全下');
        }
        else if (strength > 4.5 && player.chips > callAmount*2) {
          console.log(`[AI行动] simpleAiAction决策=raise`);
          this.onRaise('牌力较强，选择加注');
        }
        else if (strength > 2.5 || callAmount < player.chips/10) {
          console.log(`[AI行动] simpleAiAction决策=call`);
          this.onCall('底池赔率合适，选择跟注');
        }
        else {
          console.log(`[AI行动] simpleAiAction决策=fold`);
          this.onFold('牌力较弱，选择弃牌');
        }
      }
    },
    
    // 通用筹码动画触发器
    triggerChipAnimation(config: {
      fromSelector?: string;
      toSelector?: string;
      fromRect?: any;
      toRect?: any;
      playerId: number;
      amount: number;
      offset?: number;
      delay?: number;
      fromPot?: boolean;
      retry?: number;
    }) {
      const { playerId, amount, offset = 0, delay = 0, fromPot = false, retry = 0 } = config;
      const phase = (this.data && this.data.gameState && this.data.gameState.gamePhase) ? this.data.gameState.gamePhase : 'unknown';
      const animKey = `${playerId}_${amount}_${phase}_${offset}`;
      
      if (_betAnimSet.has(animKey)) return;
      _betAnimSet.add(animKey);
      
      if (amount <= 0) return;
      
      // 如果已提供位置信息，直接创建动画
      if (config.fromRect && config.toRect) {
        this.createChipAnimation(config.fromRect, config.toRect, playerId, amount, fromPot, offset, delay, animKey);
        return;
      }
      
      // 否则查询位置
      const query = wx.createSelectorQuery().in(this);
      if (fromPot) {
        query.select('.pot').boundingClientRect();
        query.selectAll('.player').boundingClientRect();
      } else {
        query.select('.scrollarea').scrollOffset();
        query.selectAll('.player').boundingClientRect();
        query.selectAll('.player').fields({dataset: true});
        query.select('.pot').boundingClientRect();
      }
      
      query.exec((res) => {
        let fromRect, toRect;
        
        if (fromPot) {
          const [potRect, playerRects] = res;
          const playerIndex = this.data.gameState.players.findIndex((p: any) => String(p.id) === String(playerId));
          fromRect = potRect;
          toRect = playerRects[playerIndex];
        } else {
          const [scrollRes, rects, nodes, potRect] = res;
          const scrollTop = (scrollRes && scrollRes.scrollTop) ? scrollRes.scrollTop : 0;
          const idx = nodes.findIndex((node: any) => String(node.dataset.playerId) === String(playerId));
          const playerRect = rects[idx];
          
          fromRect = playerRect ? {
            left: playerRect.left,
            top: playerRect.top - scrollTop,
            width: playerRect.width,
            height: playerRect.height
          } : null;
          toRect = potRect ? {
            left: potRect.left,
            top: potRect.top - scrollTop,
            width: potRect.width,
            height: potRect.height
          } : null;
        }
        
        if ((!fromRect || !toRect) && retry < 3) {
          setTimeout(() => {
            const newConfig = Object.assign({}, config);
            newConfig.retry = retry + 1;
            this.triggerChipAnimation(newConfig);
          }, 80);
          return;
        }
        
        if (!fromRect || !toRect) {
          _betAnimSet.delete(animKey);
          return;
        }
        
        this.createChipAnimation(fromRect, toRect, playerId, amount, fromPot, offset, delay, animKey);
      });
    },
    
    // 创建筹码动画
    createChipAnimation(fromRect: any, toRect: any, playerId: number, amount: number, fromPot: boolean, offset: number, delay: number, animKey: string) {
      const start = { 
        left: fromRect.left + fromRect.width / 2 + offset, 
        top: fromRect.top + fromRect.height / 2 
      };
      const end = { 
        left: toRect.left + toRect.width / 2, 
        top: toRect.top + toRect.height / 2 
      };
      const key = Date.now() + '_' + Math.random();
      const newAnim = { playerId, amount, key, start, end, toEnd: false, fromPot };
      
      const executeAnimation = () => {
        const bettingAnimations = this.data.bettingAnimations.concat([newAnim]);
        this.setData({ bettingAnimations }, () => {
          setTimeout(() => {
            const updated = this.data.bettingAnimations.map((a: any) => {
              if (a.key === key) {
                const newA = Object.assign({}, a);
                newA.toEnd = true;
                return newA;
              }
              return a;
            });
            this.setData({ bettingAnimations: updated });
          }, 16);
          
          setTimeout(() => {
            _betAnimSet.delete(animKey);
            this.setData({ 
              bettingAnimations: this.data.bettingAnimations.filter((a: any) => a.key !== key) 
            });
          }, 1000);
        });
      };
      
      if (delay > 0) {
        setTimeout(executeAnimation, delay);
      } else {
        executeAnimation();
      }
    },
    
    // 下注动画（重构后）
    triggerBettingAnimation(playerId: number, amount: number, retry = 0, offset = 0) {
      this.triggerChipAnimation({
        playerId,
        amount,
        offset,
        retry,
        fromPot: false
      });
    },
    
    // 主池飞向玩家动画（重构后）
    triggerPotToPlayerAnimation(playerId: number, amount: number, offset = 0, delay = 0) {
      this.triggerChipAnimation({
        playerId,
        amount,
        offset,
        delay,
        fromPot: true
      });
    },
    onLoad(options: any) {
      // 支持从设置页跳转参数
      const playerCount = Number(options.playerCount) || 3;
      const startingChips = Number(options.startingChips) || 1000;
      const smallBlind = Number(options.smallBlind) || 20;
      const bigBlind = Number(options.bigBlind) || 40;
      const actionWaitSeconds = Number(options.actionWaitSeconds) || 5;
      const endHandWaitSeconds = Number(options.endHandWaitSeconds) || 30;
      const squidMode = options.squidMode === '1' || options.squidMode === 1;
      const squidPenaltyMultiplier = Number(options.squidPenaltyMultiplier) || 5;
      const teachingMode = options.teachingMode === '1' || options.teachingMode === 1;
      const battleReportEnabled = options.battleReportEnabled === '1' || options.battleReportEnabled === 1;

      // 初始化新功能
      this.initializeNewFeatures(teachingMode, battleReportEnabled);

      // 初始化游戏参数
      this.initGameParams(playerCount, startingChips, smallBlind, bigBlind, actionWaitSeconds, endHandWaitSeconds);

      // 初始化鱿鱼模式
      this.initSquidMode(squidMode, squidPenaltyMultiplier);

      // 关键：初始化游戏
      this.startGame();

      // 辅助思考功能已删除
    },
    initGameParams(playerCount: number, startingChips: number, smallBlind: number, bigBlind: number, actionWaitSeconds = 5, endHandWaitSeconds = 30) {
      this.setData({
        playerCount,
        startingChips,
        smallBlind,
        bigBlind,
        actionWaitSeconds,
        endHandWaitSeconds
      });
    },

    /**
     * 初始化鱿鱼模式
     */
    initSquidMode(enabled: boolean, penaltyMultiplier: number) {
      this.setData({
        squidMode: enabled,
        squidPenaltyMultiplier: penaltyMultiplier,
        squidHolders: []
      });

      if (enabled) {
        console.log(`[鱿鱼模式] 已启用，惩罚倍数: ${penaltyMultiplier}x大盲注`);
      }
    },

    /**
     * 初始化新功能
     */
    initializeNewFeatures(teachingMode: boolean = false, battleReportEnabled: boolean = false) {
      // 初始化用户管理器
      const userManager = UserManager.getInstance();
      let userProfile = userManager.getCurrentUser();

      // 如果没有用户资料，创建新用户
      if (!userProfile) {
        userManager.initializeUser().then(profile => {
          this.setData({ userProfile: profile });
        }).catch(error => {
          console.error('[Game] 初始化用户失败:', error);
        });
      } else {
        this.setData({ userProfile });
      }

      // 初始化教学模式
      const teachingManager = TeachingManager.getInstance();
      teachingManager.setTeachingMode(teachingMode);
      this.setData({ teachingMode, battleReportEnabled });




    },

    /**
     * 开始游戏记录
     */
    startGameRecord() {
      const statsManager = StatisticsManager.getInstance();
      const gameConfig = {
        startingChips: this.data.startingChips,
        smallBlind: this.data.smallBlind,
        bigBlind: this.data.bigBlind,
        teachingMode: this.data.teachingMode
      };

      const gameId = statsManager.startGameRecord(this.data.gameState, gameConfig);
      this.setData({ currentGameId: gameId });
    },

    /**
     * 记录玩家操作
     */
    recordPlayerAction(action: string, amount?: number) {
      if (!this.data.currentGameId) return;

      const statsManager = StatisticsManager.getInstance();
      statsManager.recordAction(this.data.currentGameId, action, amount, this.data.gameState);


    },

    /**
     * 获取教学提示（本地分析）
     */
    getTeachingHints() {
      if (!this.data.teachingMode) return;

      const teachingManager = TeachingManager.getInstance();
      const humanPlayer = this.data.gameState.players.find((p: any) => !p.isAI);

      if (humanPlayer) {
        // 使用本地详细分析
        const allHints = teachingManager.analyzeGameState(this.data.gameState, humanPlayer);
        this.updateTeachingHintsDisplay(allHints);
      }
    },

    /**
     * 更新教学提示显示
     */
    updateTeachingHintsDisplay(hints: any[]) {
      // 按优先级分组
      const highPriorityHints = hints.filter((hint: any) => hint.priority === 'high');
      const mediumPriorityHints = hints.filter((hint: any) => hint.priority === 'medium');
      const lowPriorityHints = hints.filter((hint: any) => hint.priority === 'low');

      this.setData({
        teachingHints: hints,
        highPriorityHints,
        mediumPriorityHints,
        lowPriorityHints
      });
    },



    /**
     * 操作前教学指导（使用本地分析 + AI建议）
     */
    providePreActionTeaching(player: any, callback: () => void) {
      if (!this.data.teachingMode) {
        callback();
        return;
      }

      // 检查本手牌是否已经显示过教学
      if (this.data.teachingShownThisHand) {
        callback();
        return;
      }

      // 标记本手牌已显示教学
      this.setData({ teachingShownThisHand: true });



      // 显示教学面板，但先显示loading状态
      this.setData({
        showTeachingPanel: true,
        teachingLoading: true,
        teachingHints: [],
        aiSuggestion: null,
        aiThinking: false,
        loadingProgress: 0
      });

      // 启动进度条动画
      this.startLoadingProgress();

      const teachingManager = TeachingManager.getInstance();

      // 检查缓存
      const cachedHints = teachingManager.checkCache(this.data.gameState, player);
      if (cachedHints) {

        // 即使有缓存，也要等AI建议完成后一起显示
        this.getAISuggestionForTeaching(player, () => {
          this.stopLoadingProgress();
          setTimeout(() => {
            this.setData({ teachingLoading: false });
            this.updateTeachingHintsDisplay(cachedHints);
            callback();
          }, 500); // 让进度条完成动画
        });
        return;
      }

      // 获取本地详细分析
      const allHints = teachingManager.analyzeGameState(this.data.gameState, player);

      // 保存到缓存
      teachingManager.saveToCache(this.data.gameState, player, allHints);

      // 获取AI建议，完成后一起显示所有内容
      this.getAISuggestionForTeaching(player, () => {
        this.stopLoadingProgress();
        setTimeout(() => {
          this.setData({ teachingLoading: false });
          this.updateTeachingHintsDisplay(allHints);
          callback();
        }, 500); // 让进度条完成动画
      });
    },

    /**
     * 启动Loading进度条动画
     */
    startLoadingProgress() {
      this.setData({ loadingProgress: 0 });

      // 清除可能存在的旧定时器
      if (this.loadingTimer) {
        clearInterval(this.loadingTimer);
      }

      // 每秒增加5%进度
      this.loadingTimer = setInterval(() => {
        const currentProgress = this.data.loadingProgress;
        if (currentProgress < 100) {
          this.setData({
            loadingProgress: Math.min(currentProgress + 5, 100)
          });
        }
      }, 1000);
    },

    /**
     * 停止Loading进度条动画
     */
    stopLoadingProgress() {
      if (this.loadingTimer) {
        clearInterval(this.loadingTimer);
        this.loadingTimer = null;
      }
      // 立即完成进度条
      this.setData({ loadingProgress: 100 });
    },

    /**
     * 为教学模式获取AI建议
     */
    getAISuggestionForTeaching(player: any, callback: () => void) {
      // 如果已经有AI建议，直接回调
      if (this.data.aiSuggestion && !this.data.aiThinking) {
        callback();
        return;
      }

      // 设置思考状态
      this.setData({
        aiThinking: true,
        aiSuggestion: null
      });

      // 调用AI获取建议
      this.makeLLMRequest('suggestion', player, {
        callback: () => {
          callback();
        }
      });
    },

    /**
     * 每次玩家行动后更新教学数据
     */
    updateTeachingDataAfterAction(playerIdx: number) {
      const gameState = this.data.gameState;
      const player = gameState.players[playerIdx];

      // 更新游戏统计数据
      if (!gameState.teachingStats) {
        gameState.teachingStats = {
          totalActions: 0,
          playerActions: {},
          phaseActions: {},
          potHistory: [],
          actionHistory: []
        };
      }

      gameState.teachingStats.totalActions++;

      // 记录玩家行动统计
      if (!gameState.teachingStats.playerActions[player.id]) {
        gameState.teachingStats.playerActions[player.id] = 0;
      }
      gameState.teachingStats.playerActions[player.id]++;

      // 记录阶段行动统计
      const phase = gameState.gamePhase;
      if (!gameState.teachingStats.phaseActions[phase]) {
        gameState.teachingStats.phaseActions[phase] = 0;
      }
      gameState.teachingStats.phaseActions[phase]++;

      // 记录底池变化
      gameState.teachingStats.potHistory.push({
        phase,
        pot: gameState.pot,
        timestamp: Date.now()
      });

      // 更新位置信息（只考虑未淘汰玩家）
      this.updatePlayerPositions();

      console.log(`[教学数据更新] 玩家${player.name}行动后，总行动数: ${gameState.teachingStats.totalActions}`);
    },



    /**
     * 操作后决策评估（本地分析）
     */
    evaluatePlayerDecision(action: string, amount?: number) {
      if (!this.data.teachingMode) return;



      // 延迟评估，让操作动画完成
      setTimeout(() => {
        const player = this.data.gameState.players[0]; // 人类玩家
        const teachingManager = TeachingManager.getInstance();

        // 使用本地逻辑评估决策
        const evaluation = teachingManager.evaluateDecision(this.data.gameState, player, action, amount);

        if (evaluation) {
          // 创建决策评估提示
          const evaluationHint = {
            type: evaluation.score >= 80 ? 'tip' : evaluation.score >= 60 ? 'explanation' : 'warning' as const,
            title: `📊 决策评估: ${evaluation.score}分`,
            content: `您刚才的${this.getActionText(action)}决策得分${evaluation.score}分。\n\n${evaluation.reason}`,
            confidence: 85,
            priority: 'medium' as const,
            module: 'evaluation'
          };

          // 添加到教学提示的末尾
          const currentHints = [...this.data.teachingHints, evaluationHint];
          this.updateTeachingHintsDisplay(currentHints);
        }
      }, 1000);
    },

    /**
     * 获取操作文本描述
     */
    getActionText(action: string): string {
      const actionMap: Record<string, string> = {
        'fold': '弃牌',
        'call': '跟注',
        'check': '看牌',
        'raise': '加注',
        'allin': '全押'
      };
      return actionMap[action] || action;
    },



    /**
     * 显示/隐藏教学面板
     */
    toggleTeachingPanel() {
      const shouldShow = !this.data.showTeachingPanel;
      this.setData({ showTeachingPanel: shouldShow });

      // 如果显示面板且没有内容，获取教学提示
      if (shouldShow && (!this.data.teachingHints || this.data.teachingHints.length === 0)) {
        this.getTeachingHints();
      }
    },

    /**
     * 关闭教学面板（带动画）
     */
    closeTeachingPanel() {
      this.setData({ teachingPanelClosing: true });
      setTimeout(() => {
        this.setData({
          showTeachingPanel: false,
          teachingPanelClosing: false
        });
      }, 300);
    },

    /**
     * 阻止点击面板内容时关闭
     */
    preventClose() {
      // 空函数，阻止事件冒泡
    },



    /**
     * 跳转到用户资料页面
     */
    goToProfile() {
      wx.navigateTo({
        url: '/pages/profile/profile'
      });
    },

    // ==========================================
    // 3. 鱿鱼模式相关方法
    // ==========================================

    /**
     * 初始化鱿鱼游戏状态
     */
    initSquidGameState() {
      if (!this.data.squidMode) return;

      const playerCount = this.data.gameState.players.length;
      const totalSquids = playerCount - 1; // 总鱿鱼数 = 玩家数 - 1
      const penaltyAmount = this.data.squidPenaltyMultiplier * this.data.bigBlind;

      // 初始化游戏状态中的鱿鱼数据
      const gameState = this.data.gameState;
      gameState.squidGame = {
        enabled: true,
        squidHolders: new Set(),
        totalSquids: totalSquids,
        penaltyAmount: penaltyAmount,
        penaltyMultiplier: this.data.squidPenaltyMultiplier
      };

      // 更新玩家鱿鱼状态
      this.updatePlayersSquidStatus();

      this.setData({
        gameState,
        squidHolders: [] // UI显示用的数组
      });

      console.log(`[鱿鱼模式] 初始化完成，总鱿鱼数: ${totalSquids}，惩罚金额: ${penaltyAmount}/人`);
    },

    /**
     * 检查玩家是否拥有鱿鱼
     */
    hasSquid(playerId: number): boolean {
      if (!this.data.squidMode || !this.data.gameState.squidGame) return true;
      return this.data.gameState.squidGame.squidHolders.has(playerId);
    },

    /**
     * 更新所有玩家的鱿鱼状态
     */
    updatePlayersSquidStatus() {
      if (!this.data.squidMode || !this.data.gameState.squidGame) return;

      const gameState = this.data.gameState;
      const squidGame = gameState.squidGame;

      // 给每个玩家添加 hasSquid 属性
      if (squidGame) {
        gameState.players.forEach((player: any) => {
          (player as any).hasSquid = squidGame.squidHolders.has(player.id);
        });
      }

      this.setData({ gameState });
    },

    /**
     * 给玩家分配鱿鱼（赢得主池时调用）
     */
    giveSquidToPlayer(playerId: number) {
      if (!this.data.squidMode || !this.data.gameState.squidGame) return;

      const gameState = this.data.gameState;
      const squidGame = gameState.squidGame;

      if (squidGame && !squidGame.squidHolders.has(playerId)) {
        squidGame.squidHolders.add(playerId);

        // 更新UI显示
        const squidHolders = Array.from(squidGame.squidHolders);
        this.updatePlayersSquidStatus();
        this.setData({ squidHolders });

        const player = gameState.players.find((p: any) => p.id === playerId);
        console.log(`[鱿鱼模式] ${player ? player.name : '未知玩家'} 获得鱿鱼🦑 (${squidHolders.length}/${squidGame.totalSquids})`);

        // 检查是否触发鱿鱼游戏结束
        this.checkSquidGameEnd();
      }
    },

    /**
     * 检查鱿鱼游戏是否结束
     */
    checkSquidGameEnd(): boolean {
      if (!this.data.squidMode || !this.data.gameState.squidGame) return false;

      const gameState = this.data.gameState;
      const squidGame = gameState.squidGame;
      if (!squidGame) return false;

      const activePlayers = gameState.players.filter((p: any) => !p.eliminated);
      const playersWithoutSquid = activePlayers.filter((p: any) => !squidGame.squidHolders.has(p.id));

      // 如果只剩一个玩家没有鱿鱼，游戏结束
      if (playersWithoutSquid.length === 1) {
        const loser = playersWithoutSquid[0];
        this.executeSquidPenalty(loser);
        return true;
      }

      return false;
    },

    /**
     * 执行鱿鱼惩罚
     */
    executeSquidPenalty(loser: Player) {
      if (!this.data.squidMode || !this.data.gameState.squidGame) return;

      const gameState = this.data.gameState;
      const squidGame = gameState.squidGame;
      if (!squidGame) return;

      const activePlayers = gameState.players.filter((p: any) => !p.eliminated && p.id !== loser.id);
      const idealTotalPenalty = squidGame.penaltyAmount * activePlayers.length;

      // 计算失败者实际能支付的金额
      const actualTotalPenalty = Math.min(loser.chips, idealTotalPenalty);
      const actualPerPlayerPenalty = activePlayers.length > 0 ? Math.floor(actualTotalPenalty / activePlayers.length) : 0;
      const remainder = actualTotalPenalty - (actualPerPlayerPenalty * activePlayers.length);

      console.log(`[${getTimeStamp()}][鱿鱼模式] ${loser.name} 成为最后一个没有鱿鱼的玩家`);
      console.log(`[${getTimeStamp()}][鱿鱼模式] 理想惩罚: ${idealTotalPenalty} 筹码，实际支付: ${actualTotalPenalty} 筹码`);

      if (actualTotalPenalty < idealTotalPenalty) {
        console.log(`[${getTimeStamp()}][鱿鱼模式] ${loser.name} 筹码不足，只能支付 ${actualTotalPenalty}/${idealTotalPenalty} 筹码`);
      }

      // 扣除失败者筹码（实际支付金额）
      loser.chips -= actualTotalPenalty;

      // 分配给其他玩家（按实际金额分配）
      activePlayers.forEach((player: any, index: number) => {
        let playerReceive = actualPerPlayerPenalty;
        // 将余数分配给前几个玩家
        if (index < remainder) {
          playerReceive += 1;
        }
        player.chips += playerReceive;
        console.log(`[${getTimeStamp()}][鱿鱼模式] ${player.name} 获得惩罚分配: ${playerReceive} 筹码`);
      });

      // 显示鱿鱼游戏结束弹窗
      this.showSquidGameEndPopup(loser, actualTotalPenalty, idealTotalPenalty);

      // 重置鱿鱼状态，开始新的鱿鱼游戏
      this.resetSquidGame();
    },

    /**
     * 重置鱿鱼游戏状态
     */
    resetSquidGame() {
      if (!this.data.squidMode || !this.data.gameState.squidGame) return;

      const gameState = this.data.gameState;
      if (gameState.squidGame) {
        gameState.squidGame.squidHolders.clear();
      }

      // 更新玩家鱿鱼状态
      this.updatePlayersSquidStatus();

      this.setData({
        gameState,
        squidHolders: []
      });

      console.log('[鱿鱼模式] 重置鱿鱼状态，开始新的鱿鱼游戏');
    },

    /**
     * 显示鱿鱼游戏结束弹窗
     */
    showSquidGameEndPopup(loser: Player, actualPenalty: number, idealPenalty?: number) {
      let content = `${loser.name} 成为最后一个没有鱿鱼的玩家\n`;

      if (idealPenalty && actualPenalty < idealPenalty) {
        // 筹码不足的情况
        content += `应支付惩罚: ${idealPenalty} 筹码\n`;
        content += `实际支付: ${actualPenalty} 筹码\n`;
        content += `(筹码不足，已支付全部剩余筹码)`;
      } else {
        // 正常支付的情况
        content += `支付惩罚: ${actualPenalty} 筹码`;
      }

      wx.showModal({
        title: '🦑 鱿鱼游戏结束',
        content: content,
        showCancel: false,
        confirmText: '继续游戏',
        success: () => {
          // 检查是否有玩家被淘汰
          this.checkPlayerElimination();
        }
      });
    },

    /**
     * 检查玩家淘汰情况
     */
    checkPlayerElimination() {
      const gameState = this.data.gameState;
      let eliminatedCount = 0;

      gameState.players.forEach((player: any) => {
        if (player.chips <= 0 && !player.eliminated) {
          player.eliminated = true;
          eliminatedCount++;
          console.log(`[${getTimeStamp()}][游戏状态] ${player.name} 筹码耗尽，被淘汰`);
        }
      });

      if (eliminatedCount > 0) {
        const remainingPlayers = gameState.players.filter((p: any) => !p.eliminated);
        console.log(`[${getTimeStamp()}][游戏状态] ${eliminatedCount} 名玩家被淘汰，剩余 ${remainingPlayers.length} 名玩家`);

        // 检查游戏结束条件（基于人类玩家状态）
        if (this.checkGameEndCondition()) {
          return;
        }
      }

      this.setData({ gameState });
    },

    /**
     * 检查游戏结束条件（基于人类玩家状态）
     */
    checkGameEndCondition(): boolean {
      const gameState = this.data.gameState;
      const humanPlayer = gameState.players.find((p: any) => !p.isAI);

      if (!humanPlayer) {
        console.log('[游戏结束] 未找到人类玩家');
        return false;
      }

      // 条件1: 人类玩家筹码为0且已被淘汰 - 游戏失败
      // 注意：全下时筹码为0但未被淘汰，需要等待手牌结算
      if (humanPlayer.chips <= 0 && humanPlayer.eliminated) {
        console.log(`[游戏结束] 人类玩家 ${humanPlayer.name} 筹码耗尽，游戏失败`);
        this.endGame(null, 'lose'); // 失败结束
        return true;
      }

      // 条件2: 人类玩家获得所有筹码 - 游戏胜利
      const totalChips = gameState.players.reduce((sum: number, p: any) => sum + p.chips, 0);
      if (humanPlayer.chips >= totalChips) {
        console.log(`[游戏结束] 人类玩家 ${humanPlayer.name} 获得所有筹码，游戏胜利`);
        this.endGame(humanPlayer, 'win'); // 胜利结束
        return true;
      }

      // 条件3: 只剩人类玩家有筹码 - 游戏胜利
      const alivePlayers = gameState.players.filter((p: any) => p.chips > 0 && !p.eliminated);
      if (alivePlayers.length === 1 && alivePlayers[0].id === humanPlayer.id) {
        console.log(`[游戏结束] 只剩人类玩家 ${humanPlayer.name} 有筹码，游戏胜利`);
        this.endGame(humanPlayer, 'win'); // 胜利结束
        return true;
      }

      return false;
    },

    /**
     * 结束游戏
     */
    endGame(winner?: Player, result?: 'win' | 'lose') {
      // 记录游戏结束统计
      this.recordGameEnd();

      const humanPlayer = this.data.gameState.players.find((p: any) => !p.isAI);

      if (result === 'win') {
        // 游戏胜利
        console.log(`[${getTimeStamp()}][游戏结束] ${humanPlayer?.name || '玩家'} 获得最终胜利！`);

        // 更新用户成就和经验
        if (humanPlayer) {
          const userManager = UserManager.getInstance();
          userManager.addExperience(200); // 获胜奖励经验

          // 检查大胜成就
          const chipsWon = humanPlayer.chips - this.data.startingChips;
          userManager.checkAchievements({ bigWin: chipsWon });
        }

        this.setData({
          gameOver: true,
          gameResult: 'win',
          'gameState.gamePhase': 'gameEnd',
          showGameEndPopup: true,
          gameEndPopupData: {
            title: '🎉 恭喜获胜！',
            content: `你成功获得了所有筹码！`,
            result: 'win',
            finalChips: humanPlayer?.chips || 0,
            duration: this.getGameDuration()
          }
        });

      } else if (result === 'lose') {
        // 游戏失败
        console.log(`[${getTimeStamp()}][游戏结束] ${humanPlayer?.name || '玩家'} 筹码耗尽，游戏失败`);

        this.setData({
          gameOver: true,
          gameResult: 'lose',
          'gameState.gamePhase': 'gameEnd',
          showGameEndPopup: true,
          gameEndPopupData: {
            title: '💸 游戏结束',
            content: `你的筹码已经耗尽！`,
            result: 'lose',
            finalChips: humanPlayer?.chips || 0,
            duration: this.getGameDuration()
          }
        });

      } else {
        // 兼容旧逻辑
        if (winner) {
          console.log(`[${getTimeStamp()}][游戏结束] ${winner.name} 获得最终胜利！`);
          wx.showModal({
            title: '🏆 游戏结束',
            content: `恭喜 ${winner.name} 获得最终胜利！`,
            showCancel: false,
            confirmText: '重新开始',
            success: () => {
              this.startGame();
            }
          });
        } else {
          console.log(`[${getTimeStamp()}][游戏结束] 游戏结束`);
          wx.showModal({
            title: '🏆 游戏结束',
            content: '游戏结束',
            showCancel: false,
            confirmText: '重新开始',
            success: () => {
              this.startGame();
            }
          });
        }
      }
    },

    /**
     * 获取游戏时长
     */
    getGameDuration(): string {
      if (!this.data.gameStartTime) {
        return '未知';
      }

      const duration = Date.now() - this.data.gameStartTime;
      const minutes = Math.floor(duration / 60000);
      const seconds = Math.floor((duration % 60000) / 1000);

      if (minutes > 0) {
        return `${minutes}分${seconds}秒`;
      } else {
        return `${seconds}秒`;
      }
    },

    /**
     * 记录游戏结束统计
     */
    recordGameEnd() {
      if (!this.data.currentGameId) return;

      const statsManager = StatisticsManager.getInstance();
      statsManager.endGameRecord(this.data.currentGameId, this.data.gameState);

      // 更新用户统计
      const userManager = UserManager.getInstance();
      const humanPlayer = this.data.gameState.players.find((p: any) => !p.isAI);
      if (humanPlayer) {
        userManager.updateUser({
          totalGames: (userManager.getCurrentUser()?.totalGames || 0) + 1,
          totalChipsWon: (userManager.getCurrentUser()?.totalChipsWon || 0) + Math.max(0, humanPlayer.chips - this.data.startingChips)
        });

        // 检查成就
        userManager.checkAchievements({
          bigWin: humanPlayer.chips - this.data.startingChips
        });
      }

      // 生成战报（如果启用）
      if (this.data.battleReportEnabled) {
        this.generateBattleReport();
      }


    },

    /**
     * 生成战报
     */
    async generateBattleReport() {
      try {
        const battleReportManager = BattleReportManager.getInstance();
        const gameHistory = this.getGameHistory();

        const gameSettings = {
          playerCount: this.data.playerCount,
          startingChips: this.data.startingChips,
          smallBlind: this.data.smallBlind,
          bigBlind: this.data.bigBlind
        };

        const battleReport = await battleReportManager.generateBattleReport(
          this.data.currentGameId,
          this.data.gameState,
          gameSettings,
          gameHistory
        );

        // 显示战报生成成功提示
        wx.showToast({
          title: '战报已生成',
          icon: 'success',
          duration: 2000
        });

      } catch (error) {
        console.error('[BattleReport] 生成战报失败:', error);
        wx.showToast({
          title: '战报生成失败',
          icon: 'none',
          duration: 2000
        });
      }
    },

    /**
     * 获取游戏历史记录
     */
    getGameHistory(): any[] {
      const history: any[] = [];
      const gameState = this.data.gameState;

      // 从actionHistory中提取历史记录
      Object.keys(gameState.actionHistory).forEach(phase => {
        const phaseActions = gameState.actionHistory[phase] || [];
        phaseActions.forEach((action: any) => {
          history.push({
            ...action,
            phase,
            timestamp: Date.now() // 简化实现
          });
        });
      });

      return history;
    },

    /**
     * 页面分享
     */
    onShareAppMessage() {
      const humanPlayer = this.data.gameState.players.find((p: any) => !p.isAI);
      const chipsWon = humanPlayer ? humanPlayer.chips - this.data.startingChips : 0;
      const result = chipsWon > 0 ? '盈利' : chipsWon < 0 ? '亏损' : '平局';
      const amount = Math.abs(chipsWon);

      let shareTitle = '🎮 德州扑克智能训练';
      if (this.data.gameState.gamePhase === 'gameEnd') {
        shareTitle = `🎮 德州扑克对局结束 - ${result}${amount > 0 ? amount + '筹码' : ''}`;
      } else {
        shareTitle = '🎮 德州扑克激烈对局进行中';
      }

      return {
        title: shareTitle,
        path: '/pages/index/index',
        imageUrl: '/images/share-game.jpg'
      };
    },

    onManualStartNewHand() {
      if (this.data.countdownTimer) {
        clearInterval(this.data.countdownTimer);
        this.setData({ countdownTimer: null });
      }
      this.setData({ countdown: 30 });
      this.startNewHand();
    },
    startCountdownForNewHand() {
      if (this.data.gameOver) return;
      if (this.data.countdownTimer) {
        clearInterval(this.data.countdownTimer);
      }
      resetGameUIState(this);
      const wait = this.data.endHandWaitSeconds || 30;
      startCountdown(this, 'countdown', wait, () => {
        this.setData({ countdownTimer: null, countdown: wait });
        this.startNewHand();
      });
    },
    onShowRaiseModal() {
      this.setData({ showRaiseModal: true, raiseInputValue: '' });
    },
    onHideRaiseModal() {
      this.setData({ showRaiseModal: false });
    },
    onRaiseInputChange(e: any) {
      this.setData({ raiseInputValue: e.detail.value });
    },
    onConfirmRaise() {
      const value = Number(this.data.raiseInputValue);
      if (!value || value <= 0) {
        wx.showToast({ title: '请输入有效的加注金额', icon: 'none' });
        return;
      }
      this.setData({ raiseAmount: value, showRaiseModal: false }, () => {
        this._doRaise();
      });
    },
    // 结算统计
    showEndHandPopupWithResult() {
      if ((this as any)._chipsBeforeSettle !== null && (this as any)._chipsBeforeSettle !== undefined) {
        console.log('[DEBUG] showEndHandPopupWithResult: 已有结算，直接return', (this as any)._chipsBeforeSettle);
        return;
      }
      console.log(`[${getTimeStamp()}][操作日志] showEndHandPopupWithResult 被调用`);
      resetGameUIState(this);
      const gameState = this.data.gameState;
      // 记录结算前每个玩家的筹码
      const chipsBefore: { [id: number]: number } = {};
      if (!(this as any)._chipsBeforeSettle) {
        gameState.players.forEach((p: any) => { chipsBefore[p.id] = p.chips; });
        (this as any)._chipsBeforeSettle = chipsBefore;
      } else {
        Object.assign(chipsBefore, (this as any)._chipsBeforeSettle);
      }
      // 统计每个玩家本局总投入
      const invest: { [id: number]: number } = {};
      for (const p of gameState.players) invest[p.id] = 0;
      [GAME_CONSTANTS.GAME_PHASES.PRE_FLOP, GAME_CONSTANTS.GAME_PHASES.FLOP, GAME_CONSTANTS.GAME_PHASES.TURN, GAME_CONSTANTS.GAME_PHASES.RIVER].forEach((phase: any) => {
        if (gameState.actionHistory[phase]) {
          gameState.actionHistory[phase].forEach((act: any) => {
            if (act.playerId !== undefined && ['call','raise','allin','bigBlind','smallBlind','bet'].includes(act.action)) {
              invest[act.playerId] += act.amount || 0;
            }
          });
        }
      });
      // 只剩一人未弃牌，特殊结算：原逻辑
      const notFolded = gameState.players.filter((p: any) => !p.folded);
      const earlyWinId = gameState._earlyWinPlayerId;
      if (notFolded.length === 1 && earlyWinId !== undefined) {
        setTimeout(() => {
          console.log('[DEBUG] showEndHandPopupWithResult: 只剩一人未弃牌，特殊结算分支');
        }, 1900);
      }
      // 用 distributePot 工具函数分配主池和边池
      const playerWin = distributePot(gameState, this.determineWinners.bind(this), this.determineWinnersForPlayers.bind(this));

      // 鱿鱼模式：给主池获胜者分配鱿鱼
      if (this.data.squidMode && gameState.pot > 0) {
        const mainWinners = this.determineWinners();
        if (mainWinners.length === 1) {
          // 只有单独获胜者才能获得鱿鱼（非平分情况）
          this.giveSquidToPlayer(mainWinners[0].id);
        }
      }
      // 1. 翻转未弃牌玩家手牌
      this.revealShowdownHands(() => {
        console.log('[DEBUG] revealShowdownHands 回调');
        // 2. 1秒后翻转公共牌
        this.revealAllCommunityCards(() => {
          console.log('[DEBUG] revealAllCommunityCards 回调');
          // 3. 1秒后主池筹码飞向赢家
          setTimeout(() => {
            const winners = this.determineWinners();
            const mainWin = gameState.pot > 0 && winners.length > 0 ? Math.floor(gameState.pot / winners.length) : 0;
            this.animatePotToWinners(winners, mainWin, playerWin, () => {
              console.log('[DEBUG] animatePotToWinners 回调');
              // 4. 2秒后弹出结算弹窗
              setTimeout(() => {
                console.log('[DEBUG] 调用 finishShowdownAndPopup');
                this.finishShowdownAndPopup(playerWin, invest, winners);
              }, 1000);
            });
          }, 500);
        });
      });
      // 日志：结算前所有玩家状态
      gameState.players.forEach((p: any, i: number) => {
        console.log(`[LOG] [showEndHandPopupWithResult] 玩家${i} name=${p.name} folded=${p.folded} isAllIn=${p.isAllIn} chips=${p.chips} eliminated=${p.eliminated}`);
      });
    },
    // 新增：翻转未弃牌玩家手牌动画
    revealShowdownHands(cb: () => void) {
      setTimeout(cb, 600);
    },
    // 新增：翻转所有公共牌动画
    revealAllCommunityCards(cb: () => void) {
      setTimeout(cb, 800);
    },
    // 新增：主池筹码飞向赢家动画
    animatePotToWinners(winners: any[], mainWin: number, playerWin: Record<number, number>, cb: () => void) {
      if (mainWin > 0) {
        winners.forEach((w: any, i: number) => {
          setTimeout(() => {
            this.triggerPotToPlayerAnimation(w.id, mainWin, 0, i * 100);
          }, i * 100);
        });
      }
      setTimeout(cb, 1000);
    },
    // 新增：结算弹窗和数据分配
    finishShowdownAndPopup(playerWin: Record<number, number>, invest: Record<number, number>, winners: any[]) {
      console.log('[DEBUG] finishShowdownAndPopup 被调用', { playerWin, invest, winners });
      const gameState = this.data.gameState;
      winners.forEach((w: any) => { w.chips += playerWin[w.id]; });
      gameState.pot = 0;
      // 重新计算结算结果
      const result = gameState.players.map((p: any) => {
        let net = playerWin[p.id] - invest[p.id];
        if (net < -invest[p.id]) net = -invest[p.id];
        return {
          name: p.name,
          amount: net,
          type: '本局结算'
        };
      });
      const myPlayer = gameState.players.find((p: any) => p.id === 0);
      let myNet = myPlayer ? playerWin[0] - invest[0] : 0;
      if (myNet < -invest[0]) myNet = -invest[0];
      const myResult = myPlayer ? {
        win: myNet > 0,
        amount: Math.abs(myNet)
      } : { win: false, amount: 0 };
      // 摊牌展示
      const showdownPlayers = gameState.players.filter((p: any) => !p.folded).map((p: any) => {
        let handEval = '';
        if (p.hand && p.hand.length === 2 && gameState.communityCards && gameState.communityCards.length >= 3) {
          const allCards = p.hand.concat(gameState.communityCards);
          const best = getBestHandCards(allCards);
          handEval = best.handEval ? best.handEval.name : '';
        }
        return {
          name: p.name,
          hand: p.hand,
          isWinner: winners.some(w => w.id === p.id),
          handEval
        };
      });
      this.setData({ endHandResult: result, myResult, showEndHandPopup: true, gameState, showdownPlayers }, () => {
        console.log('[DEBUG] finishShowdownAndPopup setData回调 showEndHandPopup:', this.data.showEndHandPopup);
        // 新增：打印所有弹窗相关变量
        console.log('[DEBUG] 弹窗状态', {
          showEndHandPopup: this.data.showEndHandPopup,
          showNewHandBtn: this.data.showNewHandBtn,
          showChampionPopup: this.data.showChampionPopup,
          showAiReasonModal: this.data.showAiReasonModal,
          showRaiseModal: this.data.showRaiseModal,
          showScoreReasonModal: this.data.showScoreReasonModal,
        });
        this.startEndHandCountdown();
        (this as any)._chipsBeforeSettle = null;
        (this as any)._newHandStarted = false;

        // 手牌结算完成后检查游戏结束条件
        setTimeout(() => {
          this.checkGameEndCondition();
        }, 100);
      });
    },
    // 面额分解并依次动画（下注和主池动画都统一）
    animateChips(playerId: number, amount: number, fromPot = false) {
      const denominations = [1000, 500, 200, 100, 50, 25, 5];
      let left = amount;
      let anims: number[] = [];
      for (const d of denominations) {
        while (left >= d) {
          anims.push(d);
          left -= d;
        }
      }
      anims.forEach((amt: any, i: number) => {
        setTimeout(() => {
          if (fromPot) {
            this.triggerPotToPlayerAnimation(playerId, amt, i * 18, i * 60);
          } else {
            this.triggerBettingAnimation(playerId, amt, 0, i * 18);
          }
        }, i * 60);
      });
    },
    // 新增：结算弹窗倒计时
    startEndHandCountdown() {
      if (this.data.gameOver) return;
      if (this.data.countdownTimer) {
        clearInterval(this.data.countdownTimer);
      }
      startCountdown(this, 'countdown', 30, () => {
        this.setData({ countdownTimer: null, countdown: 30 });
        resetGameUIState(this);
        this.startNewHand();
      });
    },
    // 判断是否应推进到下一阶段（独立方法）
    isBetRoundComplete() {
      const gameState = this.data.gameState;
      const phase = gameState.gamePhase;
      const notFoldedNotAllIn = gameState.players
        .map((p: any, i: number) => ({p, i}))
        .filter(({p}: any) => !p.folded && !p.isAllIn && p.chips > 0);
      const actedArr = Array.isArray(gameState.playersActedByPhase[phase]) ? gameState.playersActedByPhase[phase] : [];
      const allActed = notFoldedNotAllIn.every(({i}: any) => actedArr.includes(i));
      const allBetEqual = notFoldedNotAllIn.every(({p}: any) => p.bet === gameState.currentBet);
      // preFlop阶段大盲必须已行动
      if (phase === GAME_CONSTANTS.GAME_PHASES.PRE_FLOP) {
        const bbIdx = gameState.bigBlindIndex;
        const bbPlayer = gameState.players[bbIdx];
        if (!bbPlayer.folded && !bbPlayer.isAllIn && bbPlayer.chips > 0 && !actedArr.includes(bbIdx)) {
          return false;
        }
      }
      return allActed && allBetEqual;
    },
    // 检查是否只剩一个未弃牌玩家或所有活跃玩家全押，若是则直接结算或摊牌
    checkGameEndOrShowdown() {
      // 首先检查游戏结束条件（基于人类玩家状态）
      if (this.checkGameEndCondition()) {
        return true;
      }

      const gameState = this.data.gameState;
      const activePlayers = gameState.players.filter((p: any) => !p.folded && p.chips > 0);
      const allInPlayers = activePlayers.filter((p: any) => p.isAllIn);
      const nonAllInPlayers = activePlayers.filter((p: any) => !p.isAllIn);
      if (activePlayers.length <= 1) {
        // 只剩一个玩家未弃牌，直接结算
        const winner = activePlayers[0];
        if (winner) {
          this.setData({ 'gameState._earlyWinPlayerId': winner.id }, () => {
            gameState.gamePhase = GAME_CONSTANTS.GAME_PHASES.SHOWDOWN;
            this.showEndHandPopupWithResult();
          });
        } else {
          // 极端情况：所有人都弃牌
          gameState.pot = 0;
          gameState.gamePhase = GAME_CONSTANTS.GAME_PHASES.SHOWDOWN;
          this.showEndHandPopupWithResult();
        }
        return true;
      } else if (nonAllInPlayers.length === 0 && activePlayers.length > 1) {
        // 所有活跃玩家都全押，直接进入摊牌
        if (gameState.gamePhase !== GAME_CONSTANTS.GAME_PHASES.SHOWDOWN) {
          // 自动补齐剩余公共牌
          const left = 5 - gameState.communityCards.length;
          if (left > 0) {
            this.dealCommunityCardsSequentially(left, () => {
              gameState.gamePhase = GAME_CONSTANTS.GAME_PHASES.SHOWDOWN;
              this.calculatePots();
              this.setData({ gameState }, () => {
                this.showEndHandPopupWithResult();
              });
            });
          } else {
            gameState.gamePhase = GAME_CONSTANTS.GAME_PHASES.SHOWDOWN;
            this.calculatePots();
            this.setData({ gameState }, () => {
              this.showEndHandPopupWithResult();
            });
          }
        }
        return true;
      }
      return false;
    },
    getPlayerActionBubble(playerId: number) {
      return getPlayerActionBubbleUtil(this.data.gameState, playerId);
    },
    updatePlayerActionBubbles() {
      const gameState = this.data.gameState;
      const bubbles: PlayerActionBubbles = {};
      if (!gameState || !gameState.players) return;
      const phase = gameState.gamePhase;
      const history = gameState.actionHistory && gameState.actionHistory[phase] ? gameState.actionHistory[phase] : [];
      for (const player of gameState.players) {
        let bubble = '';
        for (let i = history.length - 1; i >= 0; i--) {
          const act = history[i];
          if (act.playerId === player.id) {
            bubble = getActionText(act.action, act.amount);
            break;
          }
        }
        bubbles[player.id + ''] = bubble;
      }
      this.setData({ playerActionBubbles: bubbles });

    },




    /**
     * 解析AI教学响应
     */
    parseTeachingResponse(content: string): any {
      try {
        // 尝试解析JSON响应
        const response = JSON.parse(content);
        return response;
      } catch (error) {
        console.error('[Teaching] 解析AI响应失败:', error);
        // 尝试从文本中提取结构化信息
        return this.extractTeachingFromText(content);
      }
    },

    /**
     * 从文本中提取教学信息（降级处理）
     */
    extractTeachingFromText(content: string): any {
      // 简单的文本解析逻辑
      const hints = [];
      const lines = content.split('\n').filter(line => line.trim());

      for (const line of lines) {
        if (line.includes('建议') || line.includes('推荐')) {
          hints.push({
            type: 'suggestion',
            title: 'AI建议',
            content: line.trim(),
            confidence: 80,
            priority: 'medium'
          });
        } else if (line.includes('警告') || line.includes('注意')) {
          hints.push({
            type: 'warning',
            title: '重要提醒',
            content: line.trim(),
            confidence: 85,
            priority: 'high'
          });
        }
      }

      return {
        hints: hints.slice(0, 3), // 最多3个提示
        handAnalysis: { strength: 0.5, description: '需要进一步分析' },
        situationAnalysis: { position: 'unknown', potOdds: 0 }
      };
    },
  },
})

// 工具函数：统一处理玩家操作
function handlePlayerAction(
  {
    that,
    action,
    amount = 0,
    reason = '',
    extraPlayerUpdate = (player: any, gameState: any) => {},
    afterSetData = () => {},
    animate = true,
  }: {
    that: any;
    action: string;
    amount?: number;
    reason?: string;
    extraPlayerUpdate?: (player: any, gameState: any) => void;
    afterSetData?: () => void;
    animate?: boolean;
  }
) {
  const gameState = that.data.gameState;
  const idx = gameState.currentPlayerIndex;
  const player = gameState.players[idx];

  // 记录操作前的状态，用于计算实际操作金额
  const chipsBefore = player.chips;
  const betBefore = player.bet;
  const potBefore = gameState.pot;

  // 更新玩家状态
  switch (action) {
    case 'fold': player.folded = true; break;
    case 'allin': player.isAllIn = true; break;
    // 其它状态可通过 extraPlayerUpdate
  }
  extraPlayerUpdate(player, gameState);

  // 计算实际操作金额（用于动画）
  let actualAmount = amount;
  if (amount === 0 || amount === undefined) {
    // 如果没有传入金额，通过状态变化计算实际金额
    const chipsUsed = chipsBefore - player.chips;
    const potIncrease = gameState.pot - potBefore;
    actualAmount = Math.max(chipsUsed, potIncrease);
  }

  // 写入 actionHistory
  const phase = gameState.gamePhase;
  const actObj: any = { playerId: player.id, playerName: player.name, action };
  if (actualAmount > 0) actObj.amount = actualAmount;
  if (reason) actObj.reason = reason;
  addActionHistory(gameState, phase, actObj);

  that.updatePlayerActionBubbles();

  // 日志
  const pos = getPlayerPositionName(gameState.dealerIndex, idx, gameState.players);
  const handStr = player.hand.map((c: any) => c.value + c.suit).join(', ');
  const actionText = getActionText(action, actualAmount);
  console.log(`[${getTimeStamp()}][操作日志] ${player.name}（手牌：${handStr}，位置：${pos}），${actionText}，决策原因：${reason}`);

  // 动画 - 使用实际操作金额
  if (animate && actualAmount > 0) {
    that.animateChips(player.id, actualAmount);
  }

  // setData 并推进流程
  that.setData({ gameState, lastActionBubble: { idx, action } }, () => {
    afterSetData();
    that.handleAfterPlayerAction(idx);
  });
}


